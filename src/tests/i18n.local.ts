import { makeEnvironmentProviders } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { of } from 'rxjs';
import addressConfirmation from '../assets/i18n/address-confirmation/es.json';

const NAME_MAPPING = {
  'address-confirmation': addressConfirmation,
  '': {},
  unknown: {},
} as const;

export type I18nScopeTesting = keyof typeof NAME_MAPPING;

export function provideI18NTesting(scope: I18nScopeTesting = '') {
  const text = NAME_MAPPING[scope];

  const i18nLocal = {
    getTranslateObjectByKey: ({
      key,
      scope: innerScope,
    }: {
      key: string;
      scope?: string;
    }) => {
      if (innerScope && innerScope !== scope) {
        // Handle case when component specifies a different scope than the provider
        const innerText =
          NAME_MAPPING[innerScope as I18nScopeTesting] || NAME_MAPPING.unknown;
        return of(innerText[key as keyof typeof innerText]);
      }
      return of(text[key as keyof typeof text]);
    },
    getTranslateByKey: ({ key }: { key: string }) =>
      of(text[key as keyof typeof text]),
  };

  return makeEnvironmentProviders([
    {
      provide: I18NService,
      useValue: i18nLocal,
    },
  ]);
}

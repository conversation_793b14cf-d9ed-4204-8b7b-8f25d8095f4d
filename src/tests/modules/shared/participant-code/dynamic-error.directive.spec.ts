import { Component, WritableSignal, signal } from '@angular/core';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import {
  FormControl,
  ReactiveFormsModule,
  ValidationErrors,
} from '@angular/forms';
import { By } from '@angular/platform-browser';

import { HandleParticipantCodeErrorDirective } from '../../../../app/modules/shared/participant-code/dynamic-error.directive';
import { DynamicErrorService } from '../../../../app/modules/shared/participant-code/dynamic-error.service';

@Component({
  template: `<input
    type="text"
    [formControl]="control"
    aplzHandleParticipantCodeError />`,
  imports: [ReactiveFormsModule, HandleParticipantCodeErrorDirective],
})
class TestComponent {
  control = new FormControl('');
}

describe('HandleParticipantCodeErrorDirective', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;
  let mockDynamicErrorService: {
    dynamicErrors: WritableSignal<Record<string, ValidationErrors> | null>;
  };
  let control: FormControl;

  beforeEach(async () => {
    mockDynamicErrorService = {
      dynamicErrors: signal(null),
    };

    await TestBed.configureTestingModule({
      imports: [TestComponent],
      providers: [
        { provide: DynamicErrorService, useValue: mockDynamicErrorService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    control = component.control;
    fixture.detectChanges();
  });

  it('should create the host component', () => {
    expect(component).toBeTruthy();
  });

  it('should have the directive attached to the input', () => {
    const inputEl = fixture.debugElement.query(By.css('input'));
    const directive = inputEl.injector.get(HandleParticipantCodeErrorDirective);
    expect(directive).toBeTruthy();
  });

  it('should not set errors when dynamicErrors signal is null', fakeAsync(() => {
    mockDynamicErrorService.dynamicErrors.set(null);
    control.setValue('any-code');
    tick();

    expect(control.errors).toBeNull();
  }));

  it('should not set errors when there are no errors for the current code', fakeAsync(() => {
    mockDynamicErrorService.dynamicErrors.set({
      'another-code': { someError: true },
    });
    control.setValue('any-other-code');
    tick();

    expect(control.errors).toBeNull();
  }));

  it('should set errors on the control when dynamicErrors has a corresponding error', fakeAsync(() => {
    const error = { customError: 'Invalid code' };
    mockDynamicErrorService.dynamicErrors.set({ 'error-code': error });
    control.setValue('error-code');
    tick();

    expect(control.errors).toEqual(error);
  }));

  it('should clear errors when a new value without errors is provided', fakeAsync(() => {
    const error = { customError: 'Invalid code' };
    mockDynamicErrorService.dynamicErrors.set({ 'error-code': error });

    // Set value with error
    control.setValue('error-code');
    tick();
    expect(control.errors).toEqual(error);

    // Set new value without error
    control.setValue('good-code');
    tick();
    expect(control.errors).toBeNull();
  }));
});

import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { POS_ENVIRONMENT_CORE } from '../../../../../app/core/domain/config/pos-env.core';
import {
  RaffleTicketsRequest,
  RaffleTicketsResponse,
} from '../../../../../app/modules/contest-entries/domain/raffle';
import { RaffleWithHttpRepository } from '../../../../../app/modules/contest-entries/infra/repositories/raffle-with-http.repository';

describe('RaffleWithHttpRepository', () => {
  let repository: RaffleWithHttpRepository;
  let httpMock: HttpTestingController;

  const mockEnvironment = {
    exposedPublicApiUrl: 'http://test-api.com',
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        RaffleWithHttpRepository,
        { provide: POS_ENVIRONMENT_CORE, useValue: mockEnvironment },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ],
    });

    repository = TestBed.inject(RaffleWithHttpRepository);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
  });

  it('should get raffle tickets', () => {
    const mockRequest: RaffleTicketsRequest = {
      participantId: 'test-id',
      page: 0,
      sortBy: 'CREATEDAT',
      sortDirection: 'desc',
    };

    const mockResponse: RaffleTicketsResponse = {
      content: [],
      totalElements: 0,
      totalPages: 0,
      size: 0,
      number: 0,
      numberOfElements: 0,
      first: true,
      last: true,
    };

    repository.getRaffle(mockRequest).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const url = `${mockEnvironment.exposedPublicApiUrl}/api/v1/merchant-rewards/raffle/raffle-tickets/${mockRequest.participantId}?sortBy=${mockRequest.sortBy}&sortDirection=${mockRequest.sortDirection}`;
    const req = httpMock.expectOne(
      r => r.urlWithParams === url && r.method === 'GET'
    );
    req.flush(mockResponse);
  });

  it('should handle http error', () => {
    const mockRequest: RaffleTicketsRequest = {
      participantId: 'test-id',
      page: 0,
      sortBy: 'CREATEDAT',
      sortDirection: 'desc',
    };

    repository.getRaffle(mockRequest).subscribe({
      next: () => fail('should have failed with 500 error'),
      error: error => {
        expect(error.status).toEqual(500);
      },
    });

    const url = `${mockEnvironment.exposedPublicApiUrl}/api/v1/merchant-rewards/raffle/raffle-tickets/${mockRequest.participantId}?sortBy=${mockRequest.sortBy}&sortDirection=${mockRequest.sortDirection}`;
    const req = httpMock.expectOne(
      r => r.urlWithParams === url && r.method === 'GET'
    );
    req.flush('Something went wrong', {
      status: 500,
      statusText: 'Server Error',
    });
  });
});

import { HttpClient, provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { fakeAsync, TestBed } from '@angular/core/testing';
import { provideUseCaseErrorHandlerTesting } from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../../app/core/domain/config/pos-env.core';
import { CityRepositoryResponse } from '../../../../../app/modules/contest-entries/domain/city';
import {
  ContestRegion,
  ContestRegistrationRequest,
  LoginRequest,
  LoginResponse,
  ParticipantDetailResponse,
  SendQRResponse,
} from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import {
  WinnerProfileData,
  WinnerProfileResponse,
} from '../../../../../app/modules/contest-entries/domain/winner-profile';
import { ContestWithHttpRepository } from '../../../../../app/modules/contest-entries/infra/repositories/contest-with-http.repository';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: {
          createOne: (req: ContestRegistrationRequest) => {
            return of(void 0);
          },
        },
      },
      {
        provide: POS_ENVIRONMENT_CORE,
        useValue: {
          customerRegistrationMicro: 'https://customer-registration.aplazo.net',
          exposedPublicApiUrl: 'https://core.aplazo.net',
        },
      },
      provideHttpClient(),
      provideHttpClientTesting(),
      ContestWithHttpRepository,
    ],
  });
  const httpController = TestBed.inject(HttpTestingController);
  const repository = TestBed.inject(ContestWithHttpRepository);
  const spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  const spyPost = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();

  httpController.verify();

  return { httpController, repository, spyHttp, spyPost };
};

describe('ContestWithHttpRepository', () => {
  it('should be created', () => {
    const { repository } = setup();
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(ContestWithHttpRepository);
  });

  it('should retrieve last terms and conditions link', fakeAsync(() => {
    const { repository, httpController } = setup();
    const mockResponse = { termsConditions: 'http://example.com/terms' };

    repository.retriveLastTyCLink().subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/campaign/latest-terms-and-conditions'
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);

    httpController.verify();
  }));

  it('should fetch regions', fakeAsync(() => {
    const { repository, httpController } = setup();

    const mockRegions: ContestRegion[] = [
      {
        id: 1,
        state_name: 'Region 1',
        state_cdc_code: 'CDC1',
        state_c_code: 'C1',
      },
      {
        id: 2,
        state_name: 'Region 2',
        state_cdc_code: 'CDC2',
        state_c_code: 'C2',
      },
    ];

    repository.retrieveRegions().subscribe({
      next: (regions: ContestRegion[]) => {
        expect(regions).toBeTruthy();
        expect(regions.length).toBeGreaterThan(0);
        expect(regions).toEqual(mockRegions);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://customer-registration.aplazo.net/catalog/states'
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockRegions);

    httpController.verify();
  }));

  it('should create a contest entry', fakeAsync(() => {
    const { repository, spyPost, httpController } = setup();
    const request: ContestRegistrationRequest = {
      fullName: 'John Doe',
      phoneNumber: '1234567890',
      userMail: '<EMAIL>',
      region: 'North',
      participantRole: 'Otro',
    };

    repository.createOne(request).subscribe({
      next: () => {
        expect(spyPost).toHaveBeenCalledWith(
          'https://core.aplazo.net/api/v1/merchant-rewards/sign-up',
          request
        );
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/sign-up'
    );
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(request);
    req.flush(null);

    httpController.verify();
  }));

  it('should fetch participant details', fakeAsync(() => {
    const { repository, spyHttp, httpController } = setup();
    const participantCode = '4caafb93';
    const mockDetails: ParticipantDetailResponse = {
      campaignParticipants: 17,
      currentPosition: 0,
      ranking: [],
      endDateCampaign: '2025-01-31T00:00:00',
      qr: 'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
      participantId: '4caafb93',
    };

    repository.getOneWithDetails(participantCode).subscribe({
      next: (details: ParticipantDetailResponse) => {
        expect(details).toBeTruthy();
        expect(details).toEqual(mockDetails);
        expect(spyHttp).toHaveBeenCalledWith(
          `https://core.aplazo.net/api/v1/merchant-rewards/participant-rank/${participantCode}`
        );
      },
      error: fail,
    });

    const req = httpController.expectOne(
      `https://core.aplazo.net/api/v1/merchant-rewards/participant-rank/${participantCode}`
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockDetails);

    httpController.verify();
  }));

  it('should resend participant QR code', fakeAsync(() => {
    const { repository, httpController } = setup();
    const participantId = 'some-participant-id';
    const mockResponse: SendQRResponse = {
      message: 'QR Resent',
      success: true,
    };

    repository.resendParticipantQR(participantId).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/resend-participants-qr'
    );
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({ participantId });
    req.flush(mockResponse);

    httpController.verify();
  }));

  it('should perform a winner login', fakeAsync(() => {
    const { repository, httpController } = setup();
    const credentials: LoginRequest = {
      participantId: 'some-code',
      phoneNumber: '1234567890',
    };
    const mockResponse: LoginResponse = { authToken: 'some-jwt-token' };

    repository.login(credentials).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/auth/merchant-rewards/participant/login'
    );
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(credentials);
    req.flush(mockResponse);

    httpController.verify();
  }));

  it('should get city info by zip code', fakeAsync(() => {
    const { repository, httpController } = setup();
    const zipCode = '12345';
    const mockResponse: CityRepositoryResponse[] = [
      {
        id: 1,
        postal_code: '12345',
        suburb: 'some suburb',
        municipality: 'some municipality',
        state: 'some state',
        state_cdc_code: 'some state_cdc_code',
        state_c_code: 'some state_c_code',
      },
    ];

    repository.getCityInfoByZipCode(zipCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpController.expectOne(
      `https://customer-registration.aplazo.net/catalog/postalcode/${zipCode}`
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);

    httpController.verify();
  }));

  it('should get winner profile', fakeAsync(() => {
    const { repository, httpController } = setup();
    const token = 'some-jwt-token';
    const mockResponse: WinnerProfileResponse = {
      name: 'Winner Name',
      street: 'Main St',
      exteriorNumber: '123',
      interiorNumber: 'A',
      postalCode: '12345',
      neighborhood: 'Downtown',
      addressUpdatedAt: null,
      municipality: 'some municipality',
      state: 'some state',
    };

    repository.getWinnerProfile(token).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/campaigns/winners/address-confirmation'
    );
    expect(req.request.method).toBe('GET');
    expect(req.request.headers.get('Authorization')).toBe(token);
    req.flush(mockResponse);

    httpController.verify();
  }));

  it('should confirm address', fakeAsync(() => {
    const { repository, httpController } = setup();
    const request: WinnerProfileData = {
      token: 'some-jwt-token',
      street: 'Main St',
      exteriorNumber: '123',
      interiorNumber: 'A',
      postalCode: '12345',
      neighborhood: 'Downtown',
      municipality: 'some municipality',
      state: 'some state',
      participantId: 'some-participant-id',
    };

    repository.confirmAddress(request).subscribe(() => {
      // success
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/campaigns/winners/address-confirmation'
    );
    expect(req.request.method).toBe('POST');
    const { token, ...body } = request;
    expect(req.request.body).toEqual(body);
    expect(req.request.headers.get('Authorization')).toBe(token ?? '');
    req.flush(null);

    httpController.verify();
  }));

  it('should handle forgot participant code', fakeAsync(() => {
    const { repository, httpController } = setup();
    const phone = 1234567890;

    repository.forgotParticipantCode(phone).subscribe(() => {
      // success
    });

    const req = httpController.expectOne(
      'https://core.aplazo.net/api/v1/merchant-rewards/participant?phone=1234567890'
    );
    expect(req.request.method).toBe('GET');
    req.flush(null);

    httpController.verify();
  }));
});

import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { Route, UrlSegment, UrlTree } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ROUTE_CONFIG } from '../../../../../app/core/domain/config/app-routes.core';
import { GetWinnerProfileUseCase } from '../../../../../app/modules/contest-entries/application/usecases/get-winner-profile.usecase';
import {
  WinnerProfileData,
  WinnerProfileResponse,
} from '../../../../../app/modules/contest-entries/domain/winner-profile';
import { winnerProfileMatcher } from '../../../../../app/modules/contest-entries/infra/guards/winner-profile-matcher.guard';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

const defaultProfileResponse: WinnerProfileResponse = {
  name: 'Test User',
  street: 'Test Street',
  exteriorNumber: '123',
  interiorNumber: '456',
  postalCode: '12345',
  neighborhood: 'Test Neighborhood',
  municipality: 'Test Municipality',
  state: 'Test State',
  addressUpdatedAt: '2021-01-01',
  isConfirmed: false,
};

const setup = (args?: {
  store?: WinnerProfileData;
  useCase?: WinnerProfileResponse;
}) => {
  const defaultConfig = {
    store: {
      token: null,
      participantId: null,
    },
    useCase: defaultProfileResponse,
  };

  const config = {
    store: {
      ...defaultConfig.store,
      ...args?.store,
    },
    useCase: {
      ...defaultConfig.useCase,
      ...args?.useCase,
    },
  };

  const routeSnapshotMock = {} as Route;
  const stateSnapshotMock = [] as UrlSegment[];

  TestBed.configureTestingModule({
    providers: [
      {
        provide: WinnerProfileStore,
        useValue: {
          getToken: () => config.store.token,
          state$: of(config.store),
          setState: () => {},
          clearState: () => {
            config.store = {
              token: null,
              participantId: null,
            };
          },
          setEditionFinished: () => {},
        },
      },
      {
        provide: GetWinnerProfileUseCase,
        useValue: {
          execute: () => of(config.useCase),
        },
      },
    ],
  });

  const store = TestBed.inject(WinnerProfileStore);
  const useCase = TestBed.inject(GetWinnerProfileUseCase);

  const getTokenSpy = spyOn(store, 'getToken').and.callThrough();
  const executeSpy = spyOn(useCase, 'execute').and.callThrough();
  const setStateSpy = spyOn(store, 'setState').and.callThrough();

  return {
    routeSnapshotMock,
    stateSnapshotMock,
    store,
    useCase,
    getTokenSpy,
    executeSpy,
    setStateSpy,
  };
};

describe('winnerProfileMatcher', () => {
  it('should redirect to main page when token is not available (winner notifications disabled)', async () => {
    const {
      routeSnapshotMock,
      stateSnapshotMock,
      getTokenSpy,
      executeSpy,
      setStateSpy,
    } = setup();

    const result = await TestBed.runInInjectionContext(() =>
      winnerProfileMatcher(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
    expect(getTokenSpy).not.toHaveBeenCalled();
    expect(executeSpy).not.toHaveBeenCalled();
    expect(setStateSpy).not.toHaveBeenCalled();
  });

  it('should redirect to main page when token is available (winner notifications disabled)', async () => {
    const mockToken = 'valid-token';
    const mockStore = {
      token: mockToken,
      participantId: '123',
      isConfirmed: false,
    };

    const {
      routeSnapshotMock,
      stateSnapshotMock,
      getTokenSpy,
      executeSpy,
      setStateSpy,
    } = setup({
      store: mockStore,
    });

    const result = await TestBed.runInInjectionContext(() =>
      winnerProfileMatcher(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
    expect(getTokenSpy).not.toHaveBeenCalled();
    expect(executeSpy).not.toHaveBeenCalled();
    expect(setStateSpy).not.toHaveBeenCalled();
  });

  it('should redirect to main page when usecase throws an error (winner notifications disabled)', async () => {
    const mockToken = 'valid-token';
    const {
      routeSnapshotMock,
      stateSnapshotMock,
      getTokenSpy,
      executeSpy,
      setStateSpy,
    } = setup({
      store: {
        token: mockToken,
        participantId: '123',
      },
    });

    const result = await TestBed.runInInjectionContext(() =>
      winnerProfileMatcher(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
    expect(getTokenSpy).not.toHaveBeenCalled();
    expect(executeSpy).not.toHaveBeenCalled();
    expect(setStateSpy).not.toHaveBeenCalled();
  });
});

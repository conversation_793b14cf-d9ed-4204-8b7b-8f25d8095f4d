import { TestBed } from '@angular/core/testing';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { isAddressFormEditionFinishGuard } from '../../../../../app/modules/contest-entries/infra/guards/is-address-form-edition-finish.guard';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

describe('isAddressFormEditionFinishGuard', () => {
  let mockWinnerProfileStore: jasmine.SpyObj<WinnerProfileStore>;
  let mockState$: BehaviorSubject<any>;
  let mockRoute: ActivatedRouteSnapshot;
  let mockState: RouterStateSnapshot;

  beforeEach(() => {
    mockState$ = new BehaviorSubject<any>({});
    mockWinnerProfileStore = jasmine.createSpyObj('WinnerProfileStore', [], {
      state$: mockState$,
    });

    mockRoute = {} as ActivatedRouteSnapshot;
    mockState = {} as RouterStateSnapshot;

    TestBed.configureTestingModule({
      providers: [
        { provide: WinnerProfileStore, useValue: mockWinnerProfileStore },
      ],
    });
  });

  it('should return true when finishEditing is true', async () => {
    mockState$.next({ finishEditing: true });

    const result = await TestBed.runInInjectionContext(() =>
      isAddressFormEditionFinishGuard(mockRoute, mockState)
    );

    expect(result).toBe(true);
  });

  it('should return false when finishEditing is false', async () => {
    mockState$.next({ finishEditing: false });

    const result = await TestBed.runInInjectionContext(() =>
      isAddressFormEditionFinishGuard(mockRoute, mockState)
    );

    expect(result).toBe(false);
  });

  it('should return false when finishEditing is undefined', async () => {
    mockState$.next({ finishEditing: undefined });

    const result = await TestBed.runInInjectionContext(() =>
      isAddressFormEditionFinishGuard(mockRoute, mockState)
    );

    expect(result).toBe(false);
  });

  it('should return false when finishEditing is not present in state', async () => {
    mockState$.next({});

    const result = await TestBed.runInInjectionContext(() =>
      isAddressFormEditionFinishGuard(mockRoute, mockState)
    );

    expect(result).toBe(false);
  });
});

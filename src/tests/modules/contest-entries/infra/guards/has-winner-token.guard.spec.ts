import { TestBed } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { of } from 'rxjs';
import { ROUTE_CONFIG } from '../../../../../app/core/domain/config/app-routes.core';
import { hasWinnerTokenGuard } from '../../../../../app/modules/contest-entries/infra/guards/has-winner-token.guard';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

const setup = (token: string | null = 'valid-token') => {
  const routeSnapshotMock = {} as ActivatedRouteSnapshot;
  const stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;

  TestBed.configureTestingModule({
    providers: [
      {
        provide: WinnerProfileStore,
        useValue: {
          token$: of(token),
        },
      },
      {
        provide: Router,
        useValue: jasmine.createSpyObj('Router', ['createUrlTree']),
      },
    ],
  });

  const winnerStore = TestBed.inject(WinnerProfileStore);
  const routerMock = TestBed.inject(Router) as jasmine.SpyObj<Router>;

  return {
    winnerStore,
    routerMock,
    routeSnapshotMock,
    stateSnapshotMock,
  };
};

describe('hasWinnerTokenGuard', () => {
  it('should redirect to main page when token exists (winner notifications disabled)', async () => {
    const { routerMock, routeSnapshotMock, stateSnapshotMock } = setup();

    const expectedUrlTree = new UrlTree();
    routerMock.createUrlTree.and.returnValue(expectedUrlTree);

    const result = await TestBed.runInInjectionContext(() =>
      hasWinnerTokenGuard(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBe(expectedUrlTree);
    expect(routerMock.createUrlTree).toHaveBeenCalledWith(['/']);
  });

  it('should redirect to main page when token does not exist (winner notifications disabled)', async () => {
    const { routerMock, routeSnapshotMock, stateSnapshotMock } = setup(null);

    const expectedUrlTree = new UrlTree();
    routerMock.createUrlTree.and.returnValue(expectedUrlTree);

    const result = await TestBed.runInInjectionContext(() =>
      hasWinnerTokenGuard(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBe(expectedUrlTree);
    expect(routerMock.createUrlTree).toHaveBeenCalledWith(['/']);
  });

  it('should redirect to main page when token is an empty string (winner notifications disabled)', async () => {
    const { routerMock, routeSnapshotMock, stateSnapshotMock } = setup('');

    const expectedUrlTree = new UrlTree();
    routerMock.createUrlTree.and.returnValue(expectedUrlTree);

    const result = await TestBed.runInInjectionContext(() =>
      hasWinnerTokenGuard(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBe(expectedUrlTree);
    expect(routerMock.createUrlTree).toHaveBeenCalledWith(['/']);
  });
});

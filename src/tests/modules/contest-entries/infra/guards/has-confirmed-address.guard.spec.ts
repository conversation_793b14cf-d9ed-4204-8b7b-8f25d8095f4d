import { TestBed } from '@angular/core/testing';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { ROUTE_CONFIG } from '../../../../../app/core/domain/config/app-routes.core';
import { hasConfirmedAddressGuard } from '../../../../../app/modules/contest-entries/infra/guards/has-confirmed-address.guard';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

describe('hasConfirmedAddressGuard', () => {
  let winnerProfileStoreMock: jasmine.SpyObj<WinnerProfileStore>;
  let routerMock: jasmine.SpyObj<Router>;
  let routeSnapshotMock: ActivatedRouteSnapshot;
  let stateSnapshotMock: RouterStateSnapshot;

  beforeEach(() => {
    winnerProfileStoreMock = jasmine.createSpyObj('WinnerProfileStore', [
      'getIsAddressConfirmed',
    ]);
    routerMock = jasmine.createSpyObj('Router', ['createUrlTree']);

    routeSnapshotMock = {} as ActivatedRouteSnapshot;
    stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;

    TestBed.configureTestingModule({
      providers: [
        { provide: WinnerProfileStore, useValue: winnerProfileStoreMock },
        { provide: Router, useValue: routerMock },
      ],
    });
  });

  it('should return true when address is confirmed', () => {
    winnerProfileStoreMock.getIsAddressConfirmed.and.returnValue(true);

    const result = TestBed.runInInjectionContext(() =>
      hasConfirmedAddressGuard(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBe(true);
    expect(winnerProfileStoreMock.getIsAddressConfirmed).toHaveBeenCalled();
    expect(routerMock.createUrlTree).not.toHaveBeenCalled();
  });

  it('should redirect to login when address is not confirmed', () => {
    const expectedUrlTree = new UrlTree();
    winnerProfileStoreMock.getIsAddressConfirmed.and.returnValue(false);
    routerMock.createUrlTree.and.returnValue(expectedUrlTree);

    const result = TestBed.runInInjectionContext(() =>
      hasConfirmedAddressGuard(routeSnapshotMock, stateSnapshotMock)
    );

    expect(result).toBe(expectedUrlTree);
    expect(winnerProfileStoreMock.getIsAddressConfirmed).toHaveBeenCalled();
    expect(routerMock.createUrlTree).toHaveBeenCalledWith([
      '/',
      ROUTE_CONFIG.addressConfirmation,
      ROUTE_CONFIG.loginWinner,
    ]);
  });
});

import { AsyncPipe } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideRouter, RouterLink } from '@angular/router';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { of } from 'rxjs';
import { WinnerProfileData } from '../../../../../app/modules/contest-entries/domain/winner-profile';
import { ConfirmedAddressComponent } from '../../../../../app/modules/contest-entries/infra/pages/confirmed-address/confirmed-address.component';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';
import { provideI18NTesting } from '../../../../i18n.local';

const defaultState: WinnerProfileData = {
  participantId: 'abc123',
  street: '123 Main St',
  exteriorNumber: '123',
  interiorNumber: 'A',
  postalCode: '12345',
  neighborhood: 'Downtown',
  municipality: 'New York',
  name: 'John Doe',
  isConfirmed: null,
  addressUpdatedAt: '2023-01-01T00:00:00Z',
  state: 'NY',
  token: '**********',
  finishEditing: true,
};

const setup = (args?: { state?: WinnerProfileData }) => {
  const defaultConfig = {
    state: defaultState,
  };

  const config = {
    state: args?.state ?? defaultConfig.state,
  };

  TestBed.configureTestingModule({
    imports: [
      ConfirmedAddressComponent,
      RouterLink,
      AsyncPipe,
      AplazoButtonComponent,
      AplazoIconComponent,
    ],
    providers: [
      provideI18NTesting('address-confirmation'),
      AplazoIconRegistryService,
      {
        provide: WinnerProfileStore,
        useValue: {
          state$: of(config.state),
          clearState: () => {},
        },
      },
      provideRouter([]),
    ],
  });

  const fixture = TestBed.createComponent(ConfirmedAddressComponent);
  const component = fixture.componentInstance;
  fixture.detectChanges();

  const winnerProfileStore = TestBed.inject(WinnerProfileStore);

  const spyClearState = spyOn(
    winnerProfileStore,
    'clearState'
  ).and.callThrough();

  return {
    component,
    fixture,
    spyClearState,
  };
};

describe('ConfirmedAddressComponent', () => {
  it('should create', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should use success translations when state has finishEditing=true and isConfirmed=false', () => {
    const { fixture } = setup({
      state: {
        ...defaultState,
        isConfirmed: false,
      },
    });
    fixture.detectChanges();

    const title = fixture.debugElement.query(By.css('h1')).nativeElement;
    const body = fixture.debugElement.query(By.css('p')).nativeElement;
    const backButton = fixture.debugElement.query(
      By.css('a[aplzButton] span')
    ).nativeElement;
    const img = fixture.debugElement.query(By.css('img')).nativeElement;
    const supportLink = fixture.debugElement.query(
      By.css('a[target="_blank"]')
    );

    expect(title.textContent.trim()).toBe('¡Tu información ha sido recibida!');
    expect(body.textContent.trim()).toBe(
      'Pronto recibirás tu premio en la dirección registrada.'
    );
    expect(backButton.textContent.trim()).toBe('Regresar al inicio');
    expect(img.src).toContain('success');
    expect(supportLink).toBeNull();
  });

  it('should use alreadyConfirmed translations when state has isConfirmed=true', () => {
    const { fixture } = setup({
      state: {
        ...defaultState,
        isConfirmed: true,
      },
    });
    fixture.detectChanges();

    const title = fixture.debugElement.query(By.css('h1')).nativeElement;
    const body = fixture.debugElement.query(By.css('p')).nativeElement;
    const backButton = fixture.debugElement.query(
      By.css('a[aplzButton] span')
    ).nativeElement;
    const img = fixture.debugElement.query(By.css('img')).nativeElement;
    const supportLink = fixture.debugElement.query(
      By.css('a[target="_blank"]')
    );

    expect(title.textContent.trim()).toBe(
      '¡Ya tienes una dirección confirmada!'
    );
    expect(body.textContent.trim()).toBe(
      'Tu información ya ha sido confirmada y no puede ser modificada. Si necesitas actualizar tu dirección, contacta a nuestro equipo de soporte.'
    );
    expect(backButton.textContent.trim()).toBe('Regresar al inicio');
    expect(img.src).toContain('https://cdn.aplazo.mx/b2b/error.svg');
    expect(supportLink).toBeTruthy();
    expect(supportLink.nativeElement.href).toBe(
      'https://api.whatsapp.com/send/?phone=5593020571&text&type=phone_number&app_absent=0'
    );
  });

  it('should clear store state and complete subject on destroy', () => {
    const { component, spyClearState } = setup();
    component.ngOnDestroy();
    expect(spyClearState).toHaveBeenCalled();
  });
});

import { AsyncPipe } from '@angular/common';
import {
  TestBed,
  discardPeriodicTasks,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { DialogService } from '@ngneat/dialog';
import { of, throwError } from 'rxjs';
import { ConfirmAddressUsecase } from '../../../../../app/modules/contest-entries/application/usecases/confirm-address.usecase';
import { GetCityInfoUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-city-info.usecase';
import { CityRepositoryResponse } from '../../../../../app/modules/contest-entries/domain/city';
import { WinnerProfileData } from '../../../../../app/modules/contest-entries/domain/winner-profile';
import { AddressConfirmationFormComponent } from '../../../../../app/modules/contest-entries/infra/pages/address-confirmation-form/address-confirmation-form.component';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

const defaultProfile: WinnerProfileData = {
  participantId: 'TEST123',
  name: 'Test User',
  street: 'Test Street',
  exteriorNumber: '123',
  interiorNumber: 'A',
  postalCode: '12345',
  neighborhood: 'Test Neighborhood',
  municipality: 'Test Municipality',
  state: 'Test State',
  reference: '',
  isMall: false,
};

const mockCities: CityRepositoryResponse[] = [
  {
    id: 1,
    postal_code: '12345',
    suburb: 'Neighborhood1',
    state: 'State1',
    state_cdc_code: 'ST1',
    state_c_code: 'ST1',
    municipality: 'Municipality1',
  },
  {
    id: 2,
    postal_code: '12345',
    suburb: 'Neighborhood2',
    state: 'State1',
    state_cdc_code: 'ST1',
    state_c_code: 'ST1',
    municipality: 'Municipality2',
  },
];

const setup = (args?: {
  cities?: CityRepositoryResponse[];
  profile?: WinnerProfileData;
  confirmAddressError?: boolean;
  dialogResult?: { confirmation: boolean } | null;
}) => {
  const defaultConfig = {
    cities: [],
    profile: defaultProfile,
    confirmAddressError: false,
    dialogResult: null,
  };

  const config = {
    cities: args?.cities ?? defaultConfig.cities,
    profile: args?.profile ?? defaultConfig.profile,
    confirmAddressError:
      args?.confirmAddressError ?? defaultConfig.confirmAddressError,
    dialogResult: args?.dialogResult ?? defaultConfig.dialogResult,
  };

  TestBed.configureTestingModule({
    imports: [
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      AplazoSelectComponents,
      OnlyNumbersDirective,
      AplazoTruncateLengthDirective,
      AplazoTrimSpacesDirective,
      ReactiveFormsModule,
      AsyncPipe,
    ],
    providers: [
      AplazoIconRegistryService,
      provideLoaderTesting(),
      {
        provide: DialogService,
        useValue: {
          open: () => ({
            afterClosed$: of(config.dialogResult),
          }),
        },
      },
      {
        provide: WinnerProfileStore,
        useValue: {
          getAddressWithParticipant: () => config.profile,
          state$: of(config.profile),
          getToken: () => 'test-token',
          setIsAddressConfirmed: () => {},
          setEditionFinished: () => {},
        },
      },
      {
        provide: GetCityInfoUsecase,
        useValue: {
          execute: () => of(config.cities),
        },
      },
      {
        provide: ConfirmAddressUsecase,
        useValue: {
          execute: () =>
            config.confirmAddressError
              ? throwError(() => new Error('Test error'))
              : of(void 0),
        },
      },
      {
        provide: Router,
        useValue: {
          navigate: () => Promise.resolve(true),
        },
      },
      {
        provide: DialogService,
        useValue: {
          open: () => ({
            afterClosed$: of({ confirmation: true }),
          }),
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(AddressConfirmationFormComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const store = TestBed.inject(WinnerProfileStore);
  const getCityInfoUsecase = TestBed.inject(GetCityInfoUsecase);
  const confirmAddressUsecase = TestBed.inject(ConfirmAddressUsecase);

  const confirmAddressSpy = spyOn(
    confirmAddressUsecase,
    'execute'
  ).and.callThrough();

  return { fixture, component, store, getCityInfoUsecase, confirmAddressSpy };
};

describe('AddressConfirmationFormComponent', () => {
  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should not prefill form values', fakeAsync(() => {
    const { component, fixture } = setup({ profile: {} as any });

    tick(1000);
    fixture.detectChanges();

    expect(component.addressForm.get('street')?.value).toBe('');
    expect(component.addressForm.get('exteriorNumber')?.value).toBe('');
    expect(component.addressForm.get('postalCode')?.value).toBe('');
  }));

  it('should have a form with all required fields', fakeAsync(() => {
    const { component, fixture } = setup();

    tick(1000);
    fixture.detectChanges();

    expect(component.addressForm.getRawValue()).toEqual(defaultProfile as any);
  }));

  it('should make form invalid when required fields are empty', fakeAsync(() => {
    const { component, fixture } = setup({
      profile: {
        ...defaultProfile,
        state: '',
      },
    });

    tick(1000);
    fixture.detectChanges();

    expect(component.addressForm.valid).toBeFalsy();
  }));

  it('should validate postal code length', fakeAsync(() => {
    const { component, fixture } = setup();

    component.openForm();
    tick();
    fixture.detectChanges();

    const postalCodeControl = component.zipCodeControl;

    postalCodeControl?.setValue('123');
    fixture.detectChanges();

    expect(postalCodeControl?.valid).toBeFalsy();
    expect(postalCodeControl?.hasError('minlength')).toBeTruthy();

    postalCodeControl?.setValue('12345');
    fixture.detectChanges();

    expect(postalCodeControl?.valid).toBeTruthy();
    expect(postalCodeControl?.hasError('minlength')).toBeFalsy();

    // Clean up any remaining timers
    discardPeriodicTasks();
  }));

  it('should call getCityInfo when valid postal code is entered', fakeAsync(() => {
    const { component, getCityInfoUsecase, fixture } = setup({
      profile: {
        participantId: 'TEST123',
        token: 'tokenTest',
      },
    });
    const spy = spyOn(getCityInfoUsecase, 'execute').and.callThrough();

    component.zipCodeControl.setValue('10345');

    tick(1000);
    fixture.detectChanges();

    expect(spy).toHaveBeenCalledWith('10345');
  }));

  it('should populate form with existing data if available', () => {
    const { component } = setup({
      profile: {
        participantId: 'TEST123',
        name: 'Test User',
        street: 'Test Street',
        exteriorNumber: '123',
        postalCode: '12345',
        neighborhood: 'Test Neighborhood',
        municipality: 'Test Municipality',
      },
    });

    expect(component.addressForm.get('participantId')?.value).toBe('TEST123');
    expect(component.addressForm.get('street')?.value).toBe('Test Street');
    expect(component.addressForm.get('postalCode')?.value).toBe('12345');
  });

  it('should make form valid when all required fields are filled', () => {
    const { component, fixture } = setup();

    expect(component.address).toBeDefined();
    expect(component.addressForm.disabled).toBeTruthy();

    component.openForm();
    fixture.detectChanges();

    expect(component.addressForm.disabled).toBeFalsy();
    expect(component.addressForm.valid).toBeTruthy();
  });

  it('should have disabled participantId field', () => {
    const { component } = setup();

    expect(component.addressForm.get('participantId')?.disabled).toBeTruthy();
  });

  it('should properly handle the confirmAddress method', async () => {
    const { component, fixture } = setup();

    // Fill in the form with valid values
    component.addressForm.patchValue({
      participantId: 'TEST123',
      name: 'Test User',
      street: 'Test Street',
      exteriorNumber: '123',
      postalCode: '12345',
      neighborhood: 'Test Neighborhood',
      municipality: 'Test Municipality',
      state: 'Test State',
    });

    spyOn(component, 'confirmAddress').and.callThrough();

    const button = fixture.nativeElement.querySelector('button[type="submit"]');
    button.click();
    fixture.detectChanges();

    expect(component.confirmAddress).toHaveBeenCalled();

    // cleaning up the dialog
    const dialog = document.querySelector('#aplz-ui-confirm-dialog');
    const close = dialog?.querySelector('.ngneat-close-dialog') as HTMLElement;

    close?.click();
    fixture.detectChanges();
  });

  describe('Form visibility and address card display', () => {
    it('should show the form when there are no data prefilled', fakeAsync(() => {
      const { component, fixture } = setup({ profile: {} as any });

      tick(1000);
      fixture.detectChanges();

      expect(component.showAddressForm()).toBeTruthy();
      expect(component.showUpdateAddress()).toBeFalsy();
      expect(component.showRollbackAddress()).toBeFalsy();
      expect(component.address()).toBeNull();
    }));

    it('should hide the form when there are data prefilled', fakeAsync(() => {
      const { component, fixture } = setup();

      tick(1000);
      fixture.detectChanges();

      expect(component.showAddressForm()).toBeFalsy();
      expect(component.showUpdateAddress()).toBeTruthy();
      expect(component.showRollbackAddress()).toBeFalsy();
      expect(component.address()).toBeTruthy();
    }));

    it('should show the direccion registrada card when there are data prefilled', fakeAsync(() => {
      const { fixture } = setup();

      tick(1000);
      fixture.detectChanges();

      const addressCard = fixture.nativeElement.querySelector('aplz-ui-card');
      expect(addressCard).toBeTruthy();
      expect(addressCard.textContent).toContain('Dirección Registrada');
      expect(addressCard.textContent).toContain(defaultProfile.street);
      expect(addressCard.textContent).toContain(defaultProfile.postalCode);
    }));

    it('should show the form when there are data prefilled and isEditing is enabled', fakeAsync(() => {
      const { component, fixture } = setup();

      tick(450);
      fixture.detectChanges();

      component.openForm();
      tick(451);
      fixture.detectChanges();

      expect(component.showAddressForm()).toBeTruthy();
      expect(component.showUpdateAddress()).toBeFalsy();
      expect(component.showRollbackAddress()).toBeTruthy();
      expect(component.address()).toBeTruthy();
    }));
  });

  describe('Form control state management', () => {
    it('should enable modifiable controls when opening form', fakeAsync(() => {
      const { component, fixture } = setup();

      component.openForm();
      tick(451);
      fixture.detectChanges();

      expect(component.streetControl.enabled).toBeTrue();
      expect(component.exteriorNumberControl.enabled).toBeTrue();
      expect(component.interiorNumberControl.enabled).toBeTrue();
      expect(component.zipCodeControl.enabled).toBeTrue();
      expect(component.neighborhoodControl.enabled).toBeTrue();
      expect(component.municipalityControl.enabled).toBeTrue();
      expect(component.stateControl.enabled).toBeTrue();
    }));

    it('should disable modifiable controls when form is not in editing mode', fakeAsync(() => {
      const { component, fixture } = setup();

      tick(451);
      fixture.detectChanges();

      expect(component.streetControl.disabled).toBeTrue();
      expect(component.exteriorNumberControl.disabled).toBeTrue();
      expect(component.interiorNumberControl.disabled).toBeTrue();
      expect(component.zipCodeControl.disabled).toBeTrue();
      expect(component.neighborhoodControl.disabled).toBeTrue();
      expect(component.municipalityControl.disabled).toBeTrue();
      expect(component.stateControl.disabled).toBeTrue();
    }));
  });

  describe('Address formatting', () => {
    it('should format address correctly with all fields', fakeAsync(() => {
      const { component, fixture } = setup();

      tick();
      fixture.detectChanges();

      const address = component.address();

      expect(address).toBe(
        'Test Street 123, A, Test Neighborhood, Test Municipality, Test State, C.P. 12345'
      );

      discardPeriodicTasks();
    }));

    it('should handle missing optional fields in address formatting', fakeAsync(() => {
      const { component, fixture } = setup({
        profile: {
          ...defaultProfile,
          interiorNumber: '',
        },
      });

      tick();
      fixture.detectChanges();

      const address = component.address();

      expect(address).toBe(
        'Test Street 123, Test Neighborhood, Test Municipality, Test State, C.P. 12345'
      );

      discardPeriodicTasks();
    }));

    it('should return null when required fields are missing', fakeAsync(() => {
      const { component, fixture } = setup({
        profile: {
          ...defaultProfile,
          street: '',
        },
      });

      tick();
      fixture.detectChanges();

      const address = component.address();

      expect(address).toBeNull();

      discardPeriodicTasks();
    }));
  });

  describe('Address confirmation flow', () => {
    it('should handle successful address confirmation', fakeAsync(() => {
      const { component, fixture, confirmAddressSpy } = setup();
      const routerSpy = spyOn(
        TestBed.inject(Router),
        'navigate'
      ).and.callThrough();

      component.confirmAddress();
      tick();
      fixture.detectChanges();

      expect(confirmAddressSpy).toHaveBeenCalled();
      expect(routerSpy).toHaveBeenCalled();

      discardPeriodicTasks();
    }));

    it('should handle address confirmation error', fakeAsync(() => {
      const { component, fixture } = setup({ confirmAddressError: true });
      const consoleSpy = spyOn(console, 'warn');

      component.confirmAddress();
      tick();
      fixture.detectChanges();

      expect(consoleSpy).toHaveBeenCalled();

      discardPeriodicTasks();
    }));

    it('should not proceed with confirmation if form is invalid', fakeAsync(() => {
      const { component, fixture, confirmAddressSpy } = setup();

      // First enable the form
      component.openForm();
      tick();
      fixture.detectChanges();

      // Make the form invalid
      component.addressForm.patchValue({
        street: '',
      });
      tick();
      fixture.detectChanges();

      component.confirmAddress();
      tick();
      fixture.detectChanges();

      expect(confirmAddressSpy).not.toHaveBeenCalled();

      discardPeriodicTasks();
    }));
  });

  describe('City info handling', () => {
    it('should handle city info response with multiple options', fakeAsync(() => {
      const { component, fixture } = setup({
        cities: mockCities,
        profile: {} as any,
      });

      component.zipCodeControl.setValue('12345');
      tick(component.DEBOUNCE_TIME + 1);
      fixture.detectChanges();

      let states: string[] = [];
      component.states$.subscribe(s => {
        states = s;
      });
      tick(component.DEBOUNCE_TIME + 1);
      fixture.detectChanges();

      expect(states).toContain('State1');
    }));
  });

  describe('Form state transitions', () => {
    it('should handle form state transitions correctly', fakeAsync(() => {
      const { component, fixture } = setup();

      tick();
      fixture.detectChanges();

      expect(component.showAddressForm()).toBeFalse();
      expect(component.showUpdateAddress()).toBeTrue();
      expect(component.showRollbackAddress()).toBeFalse();

      component.openForm();
      tick();
      fixture.detectChanges();

      expect(component.showAddressForm()).toBeTrue();
      expect(component.showUpdateAddress()).toBeFalse();
      expect(component.showRollbackAddress()).toBeTrue();

      discardPeriodicTasks();
    }));

    it('should handle rollback address correctly', fakeAsync(() => {
      const { component, fixture } = setup();

      component.openForm();
      tick();
      fixture.detectChanges();

      component.addressForm.patchValue({
        street: 'New Street',
        postalCode: '54321',
      });
      tick();
      fixture.detectChanges();

      component.rollbackAddress();
      tick(component.DEBOUNCE_TIME + 1);
      fixture.detectChanges();

      const streetValue = component.streetControl.value ?? '';
      const postalCodeValue = component.zipCodeControl.value ?? '';

      expect(streetValue).toBe(defaultProfile.street ?? '');
      expect(postalCodeValue).toBe(defaultProfile.postalCode ?? '');

      discardPeriodicTasks();
    }));
  });
});

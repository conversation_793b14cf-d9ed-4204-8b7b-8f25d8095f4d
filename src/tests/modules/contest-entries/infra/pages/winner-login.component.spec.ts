/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpErrorResponse } from '@angular/common/http';
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { StatsigService } from '@statsig/angular-bindings';
import { of, throwError } from 'rxjs';
import { DoWinnerLoginUseCase } from '../../../../../app/modules/contest-entries/application/usecases/do-winner-login.usecase';
import { AplazoWinnerLoginComponent } from '../../../../../app/modules/contest-entries/infra/pages/winner-login/winner-login.component';

describe('AplazoWinnerLoginComponent', () => {
  let component: AplazoWinnerLoginComponent;
  let fixture: ComponentFixture<AplazoWinnerLoginComponent>;
  let loginUseCase: jasmine.SpyObj<DoWinnerLoginUseCase>;
  let notifierService: jasmine.SpyObj<NotifierService>;
  let loaderService: jasmine.SpyObj<LoaderService>;
  let logEventSpy: jasmine.Spy;

  beforeEach(() => {
    const loginUseCaseSpy = jasmine.createSpyObj('LoginUseCase', ['execute']);
    const notifierSpy = jasmine.createSpyObj('NotifierService', ['warning']);
    const loaderSpy = jasmine.createSpyObj('LoaderService', [], {
      isLoading$: of(false),
    });
    const iconRegistrySpy = jasmine.createSpyObj('AplazoIconRegistryService', [
      'registerIcons',
    ]);

    TestBed.configureTestingModule({
      imports: [AplazoWinnerLoginComponent, RouterTestingModule],
      providers: [
        { provide: DoWinnerLoginUseCase, useValue: loginUseCaseSpy },
        { provide: NotifierService, useValue: notifierSpy },
        { provide: LoaderService, useValue: loaderSpy },
        { provide: AplazoIconRegistryService, useValue: iconRegistrySpy },
        {
          provide: StatsigService,
          useValue: {
            logEvent: () => {
              void 0;
            },
          },
        },
        {
          provide: Router,
          useValue: {
            navigate: async () => {
              void 0;
            },
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {},
        },
      ],
    });

    loginUseCase = TestBed.inject(
      DoWinnerLoginUseCase
    ) as jasmine.SpyObj<DoWinnerLoginUseCase>;
    notifierService = TestBed.inject(
      NotifierService
    ) as jasmine.SpyObj<NotifierService>;
    loaderService = TestBed.inject(
      LoaderService
    ) as jasmine.SpyObj<LoaderService>;

    fixture = TestBed.createComponent(AplazoWinnerLoginComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    const statsigService = TestBed.inject(StatsigService);
    logEventSpy = spyOn(statsigService, 'logEvent').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form controls', () => {
    expect(component.phoneNumber.value).toBe('');
    expect(component.participantId.value).toBe('');
  });

  it('should validate phone number length', () => {
    const phone = component.phoneNumber;
    phone.setValue('123');
    expect(phone.errors?.['minlength']).toBeTruthy();

    phone.setValue('12345678901');
    expect(phone.errors?.['maxlength']).toBeTruthy();

    phone.setValue('1234567890');
    expect(phone.errors).toBeNull();
  });

  it('should validate participant ID minimum length', () => {
    const participantId = component.participantId;
    participantId.setValue('123');
    expect(participantId.errors?.['minlength']).toBeTruthy();

    participantId.setValue('1234');
    expect(participantId.errors).toBeNull();
  });

  it('should not call loginUseCase if form is invalid', () => {
    if (!component.form.valid) {
      expect(loginUseCase.execute).not.toHaveBeenCalled();
    }
  });

  it('should handle login error correctly', fakeAsync(() => {
    loginUseCase.execute.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            error: {
              message: 'Login failed',
            },
          })
      )
    );

    component.participantId.setValue('1234');
    component.phoneNumber.setValue('1234567890');

    component.save();
    tick();
    fixture.detectChanges();

    expect(component.participantId.hasError('invalidCredentials')).toBeTruthy();
    expect(component.phoneNumber.hasError('invalidCredentials')).toBeTruthy();
  }));

  it('should log event on login success', fakeAsync(() => {
    loginUseCase.execute.and.returnValue(
      of({
        authToken: '1234567890',
        participantId: '1234',
      })
    );

    component.participantId.setValue('1234');
    component.phoneNumber.setValue('1234567890');

    component.save();
    tick();
    fixture.detectChanges();

    expect(logEventSpy).toHaveBeenCalledTimes(1);
    expect(logEventSpy).toHaveBeenCalledWith(
      'lpa_front_winner_login_success',
      '1234567890',
      {
        phone_number: '1234567890',
        participant_id: '1234',
      }
    );
  }));
});

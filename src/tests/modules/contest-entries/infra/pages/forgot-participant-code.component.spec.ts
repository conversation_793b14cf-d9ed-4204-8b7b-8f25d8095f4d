import { TestBed } from '@angular/core/testing';
import { FormsModule, NgForm } from '@angular/forms';
import { provideRouter, Router, RouterLink } from '@angular/router';
import {
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { Observable, of, throwError } from 'rxjs';
import {
  APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../../../app/core/domain/config/app-routes.core';
import { SendParticipantIdUsecase } from '../../../../../app/modules/contest-entries/application/usecases/send-participant-id.usecase';
import { ForgotParticipantCodeComponent } from '../../../../../app/modules/contest-entries/infra/pages/forgot-participant-code/forgot-participant-code.component';

const setup = (args?: { usecaseResp?: Observable<void> }) => {
  const defaultConfig = {
    usecaseResp: of(void 0),
  };

  const config = {
    usecaseResp:
      args && Object.prototype.hasOwnProperty.call(args, 'usecaseResp')
        ? args.usecaseResp
        : defaultConfig.usecaseResp,
  };

  TestBed.configureTestingModule({
    imports: [
      FormsModule,
      AplazoIconComponent,
      AplazoButtonComponent,
      AplazoCardComponent,
      AplazoFormFieldDirectives,
      OnlyNumbersDirective,
      AplazoTruncateLengthDirective,
      RouterLink,
    ],
    providers: [
      AplazoIconRegistryService,
      provideRouter([
        {
          path: '**',
          component: ForgotParticipantCodeComponent,
        },
      ]),

      {
        provide: APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
      {
        provide: SendParticipantIdUsecase,
        useValue: {
          execute: () => config.usecaseResp,
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(ForgotParticipantCodeComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const router = TestBed.inject(Router);
  const routerSpy = spyOn(router, 'navigate').and.callThrough();

  const usecase = TestBed.inject(SendParticipantIdUsecase);
  const usecaseSpy = spyOn(usecase, 'execute').and.callThrough();

  return {
    fixture,
    component,
    routerSpy,
    usecaseSpy,
  };
};

describe('ForgotParticipantCodeComponent', () => {
  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
    expect(component).toBeInstanceOf(ForgotParticipantCodeComponent);
  });

  it('should navigate to contest position page when send code is called with a valid form', () => {
    const { component, routerSpy, usecaseSpy } = setup();
    const testPhone = '1234567890';
    component.phone.set(testPhone);

    const form = {
      invalid: false,
      form: {
        markAllAsTouched: jasmine.createSpy('markAllAsTouched'),
      },
      reset: jasmine.createSpy('reset'),
    } as unknown as NgForm;

    component.sendCode(form);

    expect(usecaseSpy).toHaveBeenCalledWith(Number(testPhone));
    expect(routerSpy).toHaveBeenCalledWith([
      '/',
      component.routes.contestPosition,
    ]);
    expect(form.reset).toHaveBeenCalled();
  });

  it('should not call usecase or navigate when form is invalid', () => {
    const { component, routerSpy, usecaseSpy } = setup();

    const form = {
      invalid: true,
      form: {
        markAllAsTouched: jasmine.createSpy('markAllAsTouched'),
      },
    } as unknown as NgForm;

    component.sendCode(form);

    expect(form.form.markAllAsTouched).toHaveBeenCalled();
    expect(usecaseSpy).not.toHaveBeenCalled();
    expect(routerSpy).not.toHaveBeenCalled();
  });

  it('should not navigate or reset form when usecase fails', () => {
    const { component, routerSpy, usecaseSpy } = setup({
      usecaseResp: throwError(() => new Error('Usecase error')),
    });

    const testPhone = '1234567890';
    component.phone.set(testPhone);

    const form = {
      invalid: false,
      form: {
        markAllAsTouched: jasmine.createSpy('markAllAsTouched'),
      },
      reset: jasmine.createSpy('reset'),
    } as unknown as NgForm;

    component.sendCode(form);

    expect(usecaseSpy).toHaveBeenCalledWith(Number(testPhone));
    expect(routerSpy).not.toHaveBeenCalled();
    expect(form.reset).not.toHaveBeenCalled();
  });
});

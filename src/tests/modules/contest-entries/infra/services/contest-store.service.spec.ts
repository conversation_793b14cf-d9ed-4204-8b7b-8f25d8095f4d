import { TestBed } from '@angular/core/testing';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { ContestRankingUI } from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestStoreService } from '../../../../../app/modules/contest-entries/infra/services/contest-store.service';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [ContestStoreService],
  });

  const service = TestBed.inject(ContestStoreService);
  return { service };
};

describe('ContestStoreService', () => {
  it('should be created', () => {
    const { service } = setup();

    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(ContestStoreService);
  });

  it('should set new ranking', () => {
    const { service } = setup();
    const newRanking: ContestRankingUI = {
      campaignFinishDate: '2025-01-31T00:00:00',
      data: [],
      qrUrl:
        'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
      hasDetails: true,
      isFutureDate: true,
      tier: null,
    };

    service.setNewRanking(newRanking);

    service.contestRanking$.subscribe(ranking => {
      expect(ranking).toEqual(newRanking);
    });
  });

  it('should throw an error when setting new ranking with null campaignFinishDate', () => {
    const { service } = setup();
    const newRanking: ContestRankingUI = {
      campaignFinishDate: null,
      data: [],
      qrUrl:
        'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
      hasDetails: true,
      isFutureDate: true,
      tier: null,
    };

    expect(() => service.setNewRanking(newRanking)).toThrow(
      new RuntimeMerchantError(
        'La fecha de finalización de la campaña no puede ser nula',
        'ContestStoreService::setNewRanking::emptyArgument'
      )
    );
  });

  it('should throw an error when setting new ranking with null qrUrl', () => {
    const { service } = setup();
    const newRanking: ContestRankingUI = {
      campaignFinishDate: '2025-01-31T00:00:00',
      data: [],
      qrUrl: null,
      hasDetails: true,
      isFutureDate: true,
      tier: null,
    };

    expect(() => service.setNewRanking(newRanking)).toThrow(
      new RuntimeMerchantError(
        'La URL del código QR no puede ser nula',
        'ContestStoreService::setNewRanking::emptyArgument'
      )
    );
  });

  it('should clear the ranking', () => {
    const { service } = setup();
    const newRanking: ContestRankingUI = {
      campaignFinishDate: '2025-01-31T00:00:00',
      data: [],
      qrUrl:
        'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
      hasDetails: true,
      isFutureDate: true,
      tier: null,
    };

    service.setNewRanking(newRanking);

    service.clearRanking();

    service.contestRanking$.subscribe(ranking => {
      expect(ranking).toEqual({
        campaignFinishDate: null,
        data: [],
        qrUrl: null,
        hasDetails: false,
        isFutureDate: false,
        tier: null,
      });
    });
  });

  it('should return hasDetails', () => {
    const { service } = setup();
    const newRanking: ContestRankingUI = {
      campaignFinishDate: '2025-01-31T00:00:00',
      data: [],
      qrUrl:
        'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
      hasDetails: true,
      isFutureDate: true,
      tier: null,
    };

    service.setNewRanking(newRanking);

    service.hasDetails$.subscribe(hasDetails => {
      expect(hasDetails).toBe(true);
    });
  });
});

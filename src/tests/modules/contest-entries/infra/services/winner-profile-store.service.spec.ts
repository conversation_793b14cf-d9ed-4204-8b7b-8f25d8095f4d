import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { firstValueFrom } from 'rxjs';
import { skip } from 'rxjs/operators';
import { WinnerProfileData } from '../../../../../app/modules/contest-entries/domain/winner-profile';
import { WinnerProfileStore } from '../../../../../app/modules/contest-entries/infra/services/winner-profile-store.service';

describe('WinnerProfileStore', () => {
  let service: WinnerProfileStore;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [WinnerProfileStore],
    });
    service = TestBed.inject(WinnerProfileStore);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should start with default values', async () => {
    const state = await firstValueFrom(service.state$);
    expect(state).toEqual({
      token: null,
      participantId: null,
      finishEditing: null,
    });
    expect(service.getToken()).toBeNull();
    expect(service.getIsAddressConfirmed()).toBeFalse();
  });

  describe('token management', () => {
    it('should set token and emit new state', async () => {
      const token = 'test-token';
      service.setToken(token);

      expect(service.getToken()).toBe(token);

      const emittedToken = await firstValueFrom(service.token$);
      expect(emittedToken).toBe(token);
    });

    it('should not emit new state when same token is set', async () => {
      const token = 'test-token';
      service.setToken(token);

      let emitCount = 0;
      const subscription = service.state$.subscribe(() => {
        emitCount++;
      });

      service.setToken(token);
      expect(emitCount).toBe(1);

      subscription.unsubscribe();
    });
  });

  describe('participant management', () => {
    it('should set participantId and emit new state', async () => {
      const participantId = 'test-participant';
      service.setParticipantId(participantId);

      const state = await firstValueFrom(service.state$);
      expect(state.participantId).toBe(participantId);
    });

    it('should not emit new state when same participantId is set', async () => {
      const participantId = 'test-participant';
      service.setParticipantId(participantId);

      let emitCount = 0;
      const subscription = service.state$.subscribe(() => {
        emitCount++;
      });

      service.setParticipantId(participantId);
      expect(emitCount).toBe(1);

      subscription.unsubscribe();
    });
  });

  describe('address confirmation management', () => {
    it('should set isAddressConfirmed and emit new state', async () => {
      service.setIsAddressConfirmed(true);

      expect(service.getIsAddressConfirmed()).toBeTrue();
      const state = await firstValueFrom(service.state$);
      expect(state.isConfirmed).toBeTrue();
    });

    it('should not emit new state when same isAddressConfirmed is set', async () => {
      service.setIsAddressConfirmed(true);

      let emitCount = 0;
      const subscription = service.state$.subscribe(() => {
        emitCount++;
      });

      service.setIsAddressConfirmed(true);
      expect(emitCount).toBe(1);

      subscription.unsubscribe();
    });

    it('should emit isAddressConfirmed updates through isAddressConfirmed$', fakeAsync(() => {
      let emittedValue = false;

      service.isAddressConfirmed$
        .pipe(
          skip(1) // Skip the initial false value
        )
        .subscribe(value => {
          emittedValue = value;
        });

      service.setIsAddressConfirmed(true);
      tick(); // Process the emission

      expect(emittedValue).toBeTrue();
    }));
  });

  describe('state management', () => {
    it('should set full state and emit new state', async () => {
      const newState: WinnerProfileData = {
        token: 'test-token',
        participantId: 'test-participant',
        name: 'John Doe',
        street: 'Main St',
        exteriorNumber: '123',
        interiorNumber: 'A',
        postalCode: '12345',
        neighborhood: 'Downtown',
        municipality: 'Test City',
        state: 'Test State',
        addressUpdatedAt: '2023-01-01T00:00:00Z',
        isConfirmed: true,
        finishEditing: false,
      };

      service.setState(newState);

      const state = await firstValueFrom(service.state$);
      expect(state).toEqual(newState);
      expect(service.getIsAddressConfirmed()).toBeTrue();
    });

    it('should not emit new state when same state is set', async () => {
      const newState: WinnerProfileData = {
        participantId: 'test-participant',
        name: 'John Doe',
        street: 'Main St',
        exteriorNumber: '123',
        interiorNumber: 'A',
        postalCode: '12345',
        neighborhood: 'Downtown',
        municipality: 'Test City',
        state: 'Test State',
        addressUpdatedAt: '2023-01-01T00:00:00Z',
        isConfirmed: true,
      };

      service.setState(newState);

      let emitCount = 0;
      const subscription = service.state$.subscribe(() => {
        emitCount++;
      });

      service.setState({ ...newState });
      expect(emitCount).toBe(1);

      subscription.unsubscribe();
    });

    it('should get address with participant correctly', () => {
      const newState: WinnerProfileData = {
        participantId: 'test-participant',
        name: 'John Doe',
        street: 'Main St',
        exteriorNumber: '123',
        interiorNumber: 'A',
        postalCode: '12345',
        neighborhood: 'Downtown',
        municipality: 'Test City',
        state: 'Test State',
        finishEditing: false,
        reference: '',
        isMall: false,
      };

      service.setState(newState);

      const address = service.getAddressWithParticipant();
      expect(address).toEqual(newState);
    });

    it('should handle empty values in getAddressWithParticipant', () => {
      service.clearState();

      const address = service.getAddressWithParticipant();
      expect(address).toEqual({
        participantId: '',
        name: '',
        street: '',
        exteriorNumber: '',
        interiorNumber: '',
        postalCode: '',
        neighborhood: '',
        municipality: '',
        state: '',
        finishEditing: false,
        reference: '',
        isMall: false,
      });
    });
  });

  describe('clear state', () => {
    it('should reset state to default values', async () => {
      service.setToken('test-token');
      service.setParticipantId('test-participant');
      service.setIsAddressConfirmed(true);

      service.clearState();

      const state = await firstValueFrom(service.state$);
      expect(state).toEqual({
        token: null,
        participantId: null,
        finishEditing: null,
      });
      expect(service.getToken()).toBeNull();
      expect(service.getIsAddressConfirmed()).toBeFalse();
    });
  });

  describe('observables', () => {
    it('should emit token updates through token$', fakeAsync(() => {
      const token = 'test-token';
      let emittedToken: string | null = null;

      service.token$
        .pipe(
          skip(1) // Skip the initial null value
        )
        .subscribe(value => {
          emittedToken = value;
        });

      service.setToken(token);
      tick(); // Process the emission

      expect(emittedToken as any).toBe(token);
    }));

    it('should emit addressUpdatedAt through lastUpdate$', fakeAsync(() => {
      const dateStr = '2023-01-01T00:00:00Z';
      let lastUpdate: string | null = null;

      service.lastUpdate$
        .pipe(
          skip(1) // Skip the initial null value
        )
        .subscribe(value => {
          lastUpdate = value;
        });

      service.setState({
        participantId: 'test-participant',
        addressUpdatedAt: dateStr,
      });
      tick(); // Process the emission

      expect(lastUpdate as any).toEqual(dateStr);
    }));
  });
});

import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { EMPTY, isEmpty, of, take, throwError } from 'rxjs';
import { CreateOneParticipantUsecase } from '../../../../../app/modules/contest-entries/application/usecases/create-one-participant.usecase';
import {
  ContestRegistrationRequest,
  ContestRegistrationUI,
} from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: {
          createOne: (req: ContestRegistrationRequest) => {
            return of(void 0);
          },
        },
      },
      CreateOneParticipantUsecase,
    ],
  });

  const usecase = TestBed.inject(CreateOneParticipantUsecase);
  const repository = TestBed.inject(ContestRepository);
  const loader = TestBed.inject(LoaderService);
  const notifier = TestBed.inject(NotifierService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

  const successNotifierSpy = spyOn(notifier, 'success').and.callThrough();

  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    showLoaderSpy,
    hideLoaderSpy,
    successNotifierSpy,
    spyErrorHandler,
  };
};

describe('CreateOneParticipantUsecase', () => {
  it('should already been created', () => {
    const { usecase } = setup();

    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CreateOneParticipantUsecase);
  });

  it('should complete without emission when serialization throws an error', fakeAsync(() => {
    const {
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
      successNotifierSpy,
      repository,
      usecase,
    } = setup();
    const repoSpy = spyOn(repository, 'createOne').and.callThrough();

    const uiRequest: ContestRegistrationUI = {
      // @ts-expect-error: testing purposes
      fullName: undefined,
      phone: '123456789',
      region: 'Region',
      email: '<EMAIL>',
      role: 'Otro',
    };

    let result: any;

    usecase
      .execute(uiRequest)
      .pipe(take(1), isEmpty())
      .subscribe({
        next: (val) => {
          result = val;
        },
        error: fail,
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'fullName is undefined or null',
        'Guard::againstNullOrUndefined'
      ),
      EMPTY
    );

    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
    expect(repoSpy).toHaveBeenCalledTimes(0);
  }));

  it('should complete without emission when repository throws an error', fakeAsync(() => {
    const {
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
      successNotifierSpy,
      repository,
      usecase,
    } = setup();
    const err = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });
    const repoSpy = spyOn(repository, 'createOne').and.returnValue(
      throwError(() => err)
    );

    const uiRequest: ContestRegistrationUI = {
      fullName: 'Test User',
      phone: '123456789',
      region: 'Region',
      email: '<EMAIL>',
      role: 'Otro',
    };

    let result: any;

    usecase
      .execute(uiRequest)
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: (e) => {
          result = e;
        },
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(err);

    expect(repoSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));
});

import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetWinnerProfileUseCase } from '../../../../../app/modules/contest-entries/application/usecases/get-winner-profile.usecase';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import { WinnerProfileResponse } from '../../../../../app/modules/contest-entries/domain/winner-profile';

const mockWinnerProfile: WinnerProfileResponse = {
  name: 'Test User',
  street: 'Test Street',
  exteriorNumber: '123',
  interiorNumber: 'A',
  postalCode: '12345',
  neighborhood: 'Test Neighborhood',
  addressUpdatedAt: '2023-01-01T00:00:00.000Z',
  municipality: 'Test Municipality',
  state: 'Test State',
  isConfirmed: false,
};

const mockWinnerProfileWithOptionals: WinnerProfileResponse = {
  name: 'Test User',
  street: 'Test Street',
  exteriorNumber: '123',
  interiorNumber: 'A',
  postalCode: '12345',
  neighborhood: 'Test Neighborhood',
  addressUpdatedAt: '2023-01-01T00:00:00.000Z',
  municipality: 'Test Municipality',
  state: 'Test State',
  isConfirmed: true,
  reference: 'Near the park',
  isMall: true,
};

const mockWinnerProfileWithNulls: WinnerProfileResponse = {
  name: null,
  street: 'Test Street',
  exteriorNumber: '123',
  interiorNumber: 'A',
  postalCode: '12345',
  neighborhood: 'Test Neighborhood',
  addressUpdatedAt: null,
  municipality: 'Test Municipality',
  state: 'Test State',
  isConfirmed: null,
  reference: null,
  isMall: null,
};

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: jasmine.createSpyObj('ContestRepository', [
          'getWinnerProfile',
        ]),
      },
      GetWinnerProfileUseCase,
    ],
  });

  const usecase = TestBed.inject(GetWinnerProfileUseCase);
  const repository = TestBed.inject(
    ContestRepository
  ) as jasmine.SpyObj<ContestRepository>;
  const loader = TestBed.inject(LoaderService);
  const notifier = TestBed.inject(NotifierService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.returnValue('test-loader-id');
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const warningNotifierSpy = spyOn(notifier, 'warning').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    showLoaderSpy,
    hideLoaderSpy,
    warningNotifierSpy,
    spyErrorHandler,
  };
};

describe('GetWinnerProfileUseCase', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
  });

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    const {
      usecase,
      repository,
      showLoaderSpy,
      hideLoaderSpy,
      warningNotifierSpy,
      spyErrorHandler,
    } = setup();

    let result: any;
    let completed = false;

    usecase.execute('test-token').subscribe({
      next: (value: any) => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.getWinnerProfile).not.toHaveBeenCalled();
    expect(showLoaderSpy).not.toHaveBeenCalled();
    expect(hideLoaderSpy).not.toHaveBeenCalled();
    expect(warningNotifierSpy).not.toHaveBeenCalled();
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));
});

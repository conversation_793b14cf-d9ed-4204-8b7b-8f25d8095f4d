import { HttpErrorResponse } from '@angular/common/http';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { ConfirmAddressUsecase } from '../../../../../app/modules/contest-entries/application/usecases/confirm-address.usecase';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import { WinnerProfileData } from '../../../../../app/modules/contest-entries/domain/winner-profile';

describe('ConfirmAddressUsecase', () => {
  let usecase: ConfirmAddressUsecase;
  let repository: jasmine.SpyObj<ContestRepository>;
  let hideSpy: jasmine.Spy;
  let successSpy: jasmine.Spy;
  let handleErrorSpy: jasmine.Spy;

  const mockData: WinnerProfileData = {
    token: 'test-token',
    participantId: 'test-id',
    name: 'John Doe',
    street: 'Test Street',
    exteriorNumber: '123',
    interiorNumber: '',
    postalCode: '12345',
    neighborhood: 'Test Neighborhood',
    municipality: 'Test Municipality',
    state: 'Test State',
    reference: '',
    isMall: null,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ConfirmAddressUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: ContestRepository,
          useValue: jasmine.createSpyObj('ContestRepository', [
            'confirmAddress',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(ConfirmAddressUsecase);
    repository = TestBed.inject(
      ContestRepository
    ) as jasmine.SpyObj<ContestRepository>;

    const loader = TestBed.inject(LoaderService);
    const notifier = TestBed.inject(NotifierService);
    const errorHandler = TestBed.inject(UseCaseErrorHandler);

    hideSpy = spyOn(loader, 'hide').and.callThrough();
    successSpy = spyOn(notifier, 'success').and.callThrough();
    handleErrorSpy = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return empty observable when winner notifications are disabled', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when token is missing (winner notifications disabled)', fakeAsync(() => {
    const dataWithoutToken = { ...mockData, token: null };
    let result: any;
    let completed = false;

    usecase.execute(dataWithoutToken).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when participantId is missing (winner notifications disabled)', fakeAsync(() => {
    const dataWithoutParticipantId = { ...mockData, participantId: null };
    let result: any;
    let completed = false;

    usecase.execute(dataWithoutParticipantId).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when required fields are missing (winner notifications disabled)', fakeAsync(() => {
    const dataWithoutRequiredFields = { ...mockData, name: null };
    let result: any;
    let completed = false;

    usecase.execute(dataWithoutRequiredFields).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when postal code is invalid (winner notifications disabled)', fakeAsync(() => {
    const dataWithInvalidPostalCode = { ...mockData, postalCode: 'abc' };
    let result: any;
    let completed = false;

    usecase.execute(dataWithInvalidPostalCode).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when repository error (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when optional fields (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when interiorNumber field (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when reference field (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when isMall field (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when finishEditing field (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));

  it('should return empty observable when empty optional fields (winner notifications disabled)', fakeAsync(() => {
    let result: any;
    let completed = false;

    usecase.execute(mockData).subscribe({
      next: value => {
        result = value;
      },
      complete: () => {
        completed = true;
      },
    });
    tick();

    expect(result).toBeUndefined();
    expect(completed).toBe(true);
    expect(repository.confirmAddress).not.toHaveBeenCalled();
    expect(successSpy).not.toHaveBeenCalled();
    expect(hideSpy).not.toHaveBeenCalled();
    expect(handleErrorSpy).not.toHaveBeenCalled();
  }));
});

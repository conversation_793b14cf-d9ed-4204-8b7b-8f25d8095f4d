import { HttpErrorResponse } from '@angular/common/http';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { Observable, of, throwError } from 'rxjs';
import { SendParticipantIdUsecase } from '../../../../../app/modules/contest-entries/application/usecases/send-participant-id.usecase';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

const setup = (args?: { repoResponse?: Observable<unknown> }) => {
  const defaultConfig = {
    repoResponse: of(void 0),
  };

  const config = {
    repoResponse:
      args && Object.prototype.hasOwnProperty.call(args, 'repoResponse')
        ? args.repoResponse
        : defaultConfig.repoResponse,
  };

  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      SendParticipantIdUsecase,
      {
        provide: ContestRepository,
        useValue: {
          forgotParticipantCode: () => config.repoResponse,
        },
      },
    ],
  });

  const usecase = TestBed.inject(SendParticipantIdUsecase);
  const repository = TestBed.inject(ContestRepository);
  const loader = TestBed.inject(LoaderService);
  const notifier = TestBed.inject(NotifierService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const repoSpy = spyOn(repository, 'forgotParticipantCode').and.callThrough();
  const showLoaderSpy = spyOn(loader, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  const warningNotifierSpy = spyOn(notifier, 'warning').and.callThrough();
  const errorNotifierSpy = spyOn(notifier, 'error').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    showLoaderSpy,
    hideLoaderSpy,
    successNotifierSpy,
    warningNotifierSpy,
    errorNotifierSpy,
    spyErrorHandler,
    repoSpy,
  };
};

describe('SendParticipantIdUsecase', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(SendParticipantIdUsecase);
  });

  it('should manage loader lifecycle', fakeAsync(() => {
    const { usecase, showLoaderSpy, hideLoaderSpy } = setup();
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle null phone number', fakeAsync(() => {
    const { usecase, spyErrorHandler } = setup();
    const phone = null as any;

    let result: any;

    usecase.execute(phone).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });
    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'El número de teléfono es requerido',
        'SendParticipantIdUsecase::invalid::phone'
      ),
      jasmine.any(Object)
    );
  }));

  it('should handle phone number with incorrect length', fakeAsync(() => {
    const { usecase, spyErrorHandler } = setup();
    const phone = 12345;

    let result: any;

    usecase.execute(phone).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });
    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'El número de teléfono debe tener 10 dígitos',
        'SendParticipantIdUsecase::invalid::phone'
      ),
      jasmine.any(Object)
    );
  }));

  it('should call repository and show success notification on success', fakeAsync(() => {
    const { usecase, repoSpy, successNotifierSpy } = setup();
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(repoSpy).toHaveBeenCalledWith(phone);
    expect(successNotifierSpy).toHaveBeenCalledWith({
      title: 'Enviado',
      message:
        'Se ha enviado un código de recuperación a tu número de teléfono',
    });
  }));

  it('should show warning notification on 404 error', fakeAsync(() => {
    const { usecase, warningNotifierSpy } = setup({
      repoResponse: throwError(() => new HttpErrorResponse({ status: 404 })),
    });
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(warningNotifierSpy).toHaveBeenCalledWith({
      title: 'No encontramos un participante asociado a este número',
    });
  }));

  it('should show warning notification on 400 error', fakeAsync(() => {
    const { usecase, warningNotifierSpy } = setup({
      repoResponse: throwError(() => new HttpErrorResponse({ status: 400 })),
    });
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(warningNotifierSpy).toHaveBeenCalledWith({
      title: 'Parece que la información no es correcta',
      message: 'Por favor, verifica que el número de teléfono sea correcto',
    });
  }));

  it('should show error notification on other http errors', fakeAsync(() => {
    const { usecase, errorNotifierSpy } = setup({
      repoResponse: throwError(() => new HttpErrorResponse({ status: 500 })),
    });
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(errorNotifierSpy).toHaveBeenCalledWith({
      title: 'Ocurrió un error al enviar el código de recuperación',
      message: 'Estamos trabajando para resolver el problema',
    });
  }));

  it('should call error handler for non-http errors', fakeAsync(() => {
    const { usecase, spyErrorHandler } = setup({
      repoResponse: throwError(() => new Error('Some generic error')),
    });
    const phone = 1234567890;

    usecase.execute(phone).subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  }));
});

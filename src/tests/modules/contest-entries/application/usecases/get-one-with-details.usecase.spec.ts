import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { addDays, format } from 'date-fns';
import { of, take, throwError } from 'rxjs';
import { GetOneParticipantWithDetailsUseCase } from '../../../../../app/modules/contest-entries/application/usecases/get-one-with-details.usecase';
import {
  ContestRankingUI,
  ParticipantDetailResponse,
} from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

class MockTemporalService {
  today = new Date('2025-01-01');

  fromStringToDate(dateString: string): Date {
    return new Date(dateString);
  }
}

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: {
          getOneWithDetails: (participantCode: string) => {
            return throwError(new HttpErrorResponse({ status: 404 }));
          },
        },
      },
      GetOneParticipantWithDetailsUseCase,
      { provide: TemporalService, useClass: MockTemporalService },
    ],
  });

  const usecase = TestBed.inject(GetOneParticipantWithDetailsUseCase);
  const repository = TestBed.inject(ContestRepository);
  const loader = TestBed.inject(LoaderService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);
  const temporal = TestBed.inject(TemporalService);

  const showLoaderSpy = spyOn(loader, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();

  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    loader,
    errorHandler,
    temporal,
    showLoaderSpy,
    hideLoaderSpy,
    spyErrorHandler,
  };
};

describe('GetOneParticipantWithDetailsUseCase', () => {
  it('should be created', () => {
    const { usecase } = setup();

    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetOneParticipantWithDetailsUseCase);
  });

  it('should handle errors correctly', fakeAsync(() => {
    const {
      repository,
      usecase,
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
    } = setup();
    const repoSpy = spyOn(repository, 'getOneWithDetails').and.returnValue(
      throwError(() => err)
    );
    const err = new HttpErrorResponse({ error: 'Unknown error' });
    repoSpy.and.returnValue(throwError(err));

    let result: any;

    usecase
      .execute('participantCode')
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: e => {
          result = e;
        },
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(err);
    expect(repoSpy).toHaveBeenCalledTimes(1);
  }));

  it('should fetch participant details', fakeAsync(() => {
    const { repository, usecase, showLoaderSpy, hideLoaderSpy, temporal } =
      setup();
    const participantCode = 'someParticipantCode';

    const tomorrow = addDays(new Date(temporal.today), 1);
    const formattedTomorrow = format(tomorrow, "yyyy-MM-dd'T'HH:mm:ssxxx"); // Formato ISO completo

    const mockDetails: ParticipantDetailResponse = {
      participantId: '4caafb93',
      campaignParticipants: 17,
      currentPosition: 0,
      ranking: [],
      endDateCampaign: formattedTomorrow,
      qr: 'https://aplazo-rewards-merchants-stg.s3.us-west-1.amazonaws.com/participants/4caafb93.png',
    };

    spyOn(repository, 'getOneWithDetails').and.returnValue(of(mockDetails));

    let result: any;

    usecase
      .execute(participantCode)
      .pipe(take(1))
      .subscribe({
        next: (details: ContestRankingUI) => {
          result = details;
        },
        error: fail,
      });

    tick();

    const campaignFinishDate = new Date(
      result?.campaignFinishDate
    ).toLocaleDateString('es-MX');

    const today = new Date(temporal.today);
    const tomorrowDate = new Date(today);
    tomorrowDate.setDate(today.getDate() + 1);

    const isTomorrow =
      campaignFinishDate === tomorrowDate.toLocaleDateString('es-MX')
        ? 'mañana'
        : result?.campaignFinishDate;

    expect(result).toBeTruthy();
    expect(isTomorrow).toEqual('mañana');
    expect(result?.isFutureDate).toBeTrue();
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
  }));

  it('should handle an error when the participant code is invalid', fakeAsync(() => {
    const {
      repository,
      usecase,
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
    } = setup();
    const errorResponse = new RuntimeMerchantError(
      'El código del participante no existe',
      'GetOneWithDetailsUseCase::participantNotFound'
    );

    const repoSpy = spyOn(repository, 'getOneWithDetails').and.returnValue(
      throwError(() => errorResponse)
    );

    let result: any;

    usecase
      .execute('invalidParticipantCode')
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: e => {
          result = e;
        },
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'El código del participante no existe',
        'GetOneWithDetailsUseCase::participantNotFound'
      )
    );
    expect(repoSpy).toHaveBeenCalledTimes(1);
  }));
});

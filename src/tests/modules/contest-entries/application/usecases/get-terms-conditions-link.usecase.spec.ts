import { HttpErrorResponse } from '@angular/common/http';
import { SecurityContext } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetTermsConditionsLinkUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-terms-conditions-link.usecase';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

describe('GetTermsConditionsLinkUsecase', () => {
  let usecase: GetTermsConditionsLinkUsecase;
  let repository: jasmine.SpyObj<ContestRepository>;
  let sanitizer: jasmine.SpyObj<DomSanitizer>;
  let loader: LoaderService;
  let errorHandler: UseCaseErrorHandler;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let spyErrorHandler: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: ContestRepository,
          useValue: jasmine.createSpyObj('ContestRepository', [
            'retriveLastTyCLink',
          ]),
        },
        {
          provide: DomSanitizer,
          useValue: jasmine.createSpyObj('DomSanitizer', ['sanitize']),
        },
        GetTermsConditionsLinkUsecase,
      ],
    });

    usecase = TestBed.inject(GetTermsConditionsLinkUsecase);
    repository = TestBed.inject(
      ContestRepository
    ) as jasmine.SpyObj<ContestRepository>;
    sanitizer = TestBed.inject(DomSanitizer) as jasmine.SpyObj<DomSanitizer>;
    loader = TestBed.inject(LoaderService);
    errorHandler = TestBed.inject(UseCaseErrorHandler);

    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
    spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetTermsConditionsLinkUsecase);
  });

  it('should retrieve and sanitize terms and conditions link successfully', fakeAsync(() => {
    const mockLink = { termsConditions: 'https://example.com/terms' };
    const sanitizedLink = 'https://example.com/terms';

    repository.retriveLastTyCLink.and.returnValue(of(mockLink));
    sanitizer.sanitize.and.returnValue(sanitizedLink);

    let result: any;

    usecase.execute().subscribe({
      next: (link) => {
        result = link;
      },
      error: fail,
    });

    tick();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.retriveLastTyCLink).toHaveBeenCalledTimes(1);
    expect(sanitizer.sanitize).toHaveBeenCalledWith(
      SecurityContext.URL,
      mockLink.termsConditions
    );
    expect(result).toBe(sanitizedLink);
  }));

  it('should return empty string when sanitizer returns null', fakeAsync(() => {
    const mockLink = { termsConditions: 'https://example.com/terms' };

    repository.retriveLastTyCLink.and.returnValue(of(mockLink));
    sanitizer.sanitize.and.returnValue(null);

    let result: any;

    usecase.execute().subscribe({
      next: (link) => {
        result = link;
      },
      error: fail,
    });

    tick();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.retriveLastTyCLink).toHaveBeenCalledTimes(1);
    expect(sanitizer.sanitize).toHaveBeenCalledWith(
      SecurityContext.URL,
      mockLink.termsConditions
    );
    expect(result).toBe('');
  }));

  it('should handle error from repository', fakeAsync(() => {
    const errorResponse = new HttpErrorResponse({
      status: 500,
      error: { message: 'Server error' },
    });

    repository.retriveLastTyCLink.and.returnValue(
      throwError(() => errorResponse)
    );

    let errorResult: any;

    usecase.execute().subscribe({
      next: fail,
      error: (error) => {
        errorResult = error;
      },
    });

    tick();

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(repository.retriveLastTyCLink).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(errorResponse);
  }));
});

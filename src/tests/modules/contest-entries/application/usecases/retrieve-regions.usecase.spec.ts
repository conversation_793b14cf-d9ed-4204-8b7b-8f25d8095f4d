import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed } from '@angular/core/testing';
import { UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { catchError, map, Observable, of, take, throwError } from 'rxjs';
import { ContestRegionUI } from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import { GetAllContestsRegions } from '../../../../../app/modules/contest-entries/application/usecases/retrieve-regions.usecase';
import { provideUseCaseErrorHandlerTesting } from '@aplazo/merchant/shared-testing';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: {
          retrieveRegions: () => {
            return of([
              {
                id: 1,
                state_c_code: 'state_c_code',
                state_name: '',
                state_cdc_code: '',
              },
            ]);
          },
        },
      },
      GetAllContestsRegions,
    ],
  });

  const usecase = TestBed.inject(GetAllContestsRegions);
  const repository = TestBed.inject(ContestRepository);

  const errorHandler = TestBed.inject(UseCaseErrorHandler);
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    spyErrorHandler,
  };
};

describe('GetAllContestsRegions', () => {
  it('should alredy be defined', () => {
    const { usecase, spyErrorHandler } = setup();
    expect(usecase).toBeDefined();
    expect(usecase).toBeInstanceOf(GetAllContestsRegions);
  });

  it('should return all regions', fakeAsync(() => {
    const { usecase, repository } = setup();
    const err = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });

    let result: ContestRegionUI = {} as ContestRegionUI;

    usecase.execute().subscribe((regions) => {
      result = regions;
    });

    expect(result).toEqual({
      state_c_code: {
        id: 1,
        state_name: '',
        state_c_code: 'state_c_code',
        state_cdc_code: '',
      },
    });
  }));

  it('should handle errors correctly', fakeAsync(() => {
    const { usecase, repository, spyErrorHandler } = setup();

    const err = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });

    spyOn(repository, 'retrieveRegions').and.returnValue(throwError(() => err));

    let result: ContestRegionUI = {};

    usecase.execute().subscribe({
      next: (regions) => {
        result = regions;
      },
      error: fail,
    });

    expect(spyErrorHandler).toHaveBeenCalled();
    expect(result).toEqual({});
  }));

  it('should return an empty object when an error occurs', fakeAsync(() => {
    const { usecase, repository } = setup();
    spyOn(repository, 'retrieveRegions').and.returnValue(
      throwError(() => new HttpErrorResponse({ status: 500 }))
    );

    let result: ContestRegionUI = {} as ContestRegionUI;

    usecase.execute().subscribe({
      next: (regions) => {
        result = regions;
      },
      error: fail,
    });

    expect(result).toEqual({});
  }));
});

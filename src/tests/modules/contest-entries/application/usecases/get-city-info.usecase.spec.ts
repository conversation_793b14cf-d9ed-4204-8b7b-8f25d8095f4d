import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { provideUseCaseErrorHandlerTesting } from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetCityInfoUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-city-info.usecase';
import {
  CityRepositoryResponse,
  CityUI,
} from '../../../../../app/modules/contest-entries/domain/city';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

describe('GetCityInfoUsecase', () => {
  let usecase: GetCityInfoUsecase;
  let repositoryMock: jasmine.SpyObj<ContestRepository>;
  let errorHandler: jasmine.Spy;

  const citiesUI: CityUI[] = [
    {
      postalCode: '12345',
      neighborhood: 'Centro',
      municipality: 'Cuauhtémoc',
      state: 'CDMX',
    },
    {
      postalCode: '12345',
      neighborhood: 'Zapopan',
      municipality: 'Zapopan',
      state: 'Jalisco',
    },
  ];

  const citiesRepository: CityRepositoryResponse[] = [
    {
      id: 1,
      postal_code: '12345',
      suburb: 'Centro',
      municipality: 'Cuauhtémoc',
      state: 'CDMX',
      state_cdc_code: 'MX-CMX',
      state_c_code: 'CMX',
    },
    {
      id: 2,
      postal_code: '12345',
      suburb: 'Zapopan',
      municipality: 'Zapopan',
      state: 'Jalisco',
      state_cdc_code: 'MX-JAL',
      state_c_code: 'JAL',
    },
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetCityInfoUsecase,
        provideUseCaseErrorHandlerTesting(),
        {
          provide: ContestRepository,
          useValue: jasmine.createSpyObj('ContestRepository', [
            'getCityInfoByZipCode',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(GetCityInfoUsecase);
    repositoryMock = TestBed.inject(
      ContestRepository
    ) as jasmine.SpyObj<ContestRepository>;

    const handler = TestBed.inject(UseCaseErrorHandler);

    errorHandler = spyOn(handler, 'handle').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return cities when zipcode is valid', fakeAsync(() => {
    repositoryMock.getCityInfoByZipCode.and.returnValue(of(citiesRepository));
    let result: CityUI[] | undefined;

    usecase.execute('12345').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual(citiesUI);
    expect(repositoryMock.getCityInfoByZipCode).toHaveBeenCalledWith('12345');
  }));

  it('should throw error if zipcode is not a number', fakeAsync(() => {
    errorHandler.and.returnValue(of([]));
    let result: CityUI[] | undefined;

    usecase.execute('abcde').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual([]);
    expect(errorHandler).toHaveBeenCalled();
  }));

  it('should throw error if zipcode length is not 5', fakeAsync(() => {
    errorHandler.and.returnValue(of([]));
    let result: CityUI[] | undefined;

    usecase.execute('123').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual([]);
    expect(errorHandler).toHaveBeenCalled();
  }));

  it('should throw error if no cities found for zipcode', fakeAsync(() => {
    repositoryMock.getCityInfoByZipCode.and.returnValue(of([]));
    errorHandler.and.returnValue(of([]));
    let result: CityUI[] | undefined;

    usecase.execute('12345').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual([]);
    expect(repositoryMock.getCityInfoByZipCode).toHaveBeenCalledWith('12345');
    expect(errorHandler).toHaveBeenCalled();

    const error = errorHandler.calls.mostRecent()
      .args[0] as RuntimeMerchantError;
    expect(error instanceof RuntimeMerchantError).toBeTruthy();
    expect(error.message).toContain('No se encontraron ciudades');
  }));

  it('should handle repository errors', fakeAsync(() => {
    const error = new Error('Repository error');
    repositoryMock.getCityInfoByZipCode.and.returnValue(
      throwError(() => error)
    );
    errorHandler.and.returnValue(of([]));
    let result: CityUI[] | undefined;

    usecase.execute('12345').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual([]);
    expect(repositoryMock.getCityInfoByZipCode).toHaveBeenCalledWith('12345');
    expect(errorHandler).toHaveBeenCalledWith(error, jasmine.any(Object));
  }));

  it('should handle null response from repository', fakeAsync(() => {
    repositoryMock.getCityInfoByZipCode.and.returnValue(
      of(null as unknown as CityRepositoryResponse[])
    );
    errorHandler.and.returnValue(of([]));
    let result: CityUI[] | undefined;

    usecase.execute('12345').subscribe({
      next: (cities: CityUI[]) => {
        result = cities;
      },
    });

    tick();

    expect(result).toEqual([]);
    expect(repositoryMock.getCityInfoByZipCode).toHaveBeenCalledWith('12345');
    expect(errorHandler).toHaveBeenCalled();
  }));
});

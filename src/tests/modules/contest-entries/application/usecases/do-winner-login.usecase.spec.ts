import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { EMPTY, isEmpty, of, take, throwError } from 'rxjs';
import { DoWinnerLoginUseCase } from '../../../../../app/modules/contest-entries/application/usecases/do-winner-login.usecase';
import { LoginRequest } from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: ContestRepository,
        useValue: jasmine.createSpyObj('ContestRepository', ['login']),
      },
      DoWinnerLoginUseCase,
    ],
  });

  const usecase = TestBed.inject(DoWinnerLoginUseCase);
  const repository = TestBed.inject(
    ContestRepository
  ) as jasmine.SpyObj<ContestRepository>;
  const loader = TestBed.inject(LoaderService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    showLoaderSpy,
    hideLoaderSpy,
    spyErrorHandler,
  };
};

describe('LoginUseCase', () => {
  it('should already been created', () => {
    const { usecase } = setup();

    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(DoWinnerLoginUseCase);
  });

  it('should handle invalid credentials error', fakeAsync(() => {
    const { showLoaderSpy, hideLoaderSpy, repository, usecase } = setup();

    const errorResponse = new HttpErrorResponse({
      status: 400,
      error: { code: 'ACS-INVALID_ARGS' },
    });

    const repoSpy = repository.login.and.returnValue(
      throwError(() => errorResponse)
    );

    let result: any;

    usecase
      .execute({ participantId: '123', phoneNumber: '555-5555' })
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: (e) => {
          result = e;
        },
      });

    tick(1000);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);

    expect(repoSpy).toHaveBeenCalledTimes(1);
  }));

  it('should complete without emission when request is invalid', fakeAsync(() => {
    const {
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
      repository,
      usecase,
    } = setup();

    const loginRequest: LoginRequest = {
      // @ts-expect-error: testing purposes
      participantId: undefined,
      phoneNumber: '555-5555',
    };

    let result: any;

    usecase
      .execute(loginRequest)
      .pipe(take(1), isEmpty())
      .subscribe({
        next: (val) => {
          result = val;
        },
        error: fail,
      });

    tick(1000);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'El ID del participante y el número de teléfono son requeridos',
        ''
      ),
      EMPTY
    );

    expect(repository.login).toHaveBeenCalledTimes(0);
  }));

  it('should login successfully', fakeAsync(() => {
    const { showLoaderSpy, hideLoaderSpy, repository, usecase } = setup();

    repository.login.and.returnValue(
      of({
        authToken: 'test-token',
      })
    );

    let result: any;

    usecase
      .execute({ participantId: '123', phoneNumber: '555-5555' })
      .subscribe({
        next: (response) => {
          result = response;
        },
        error: fail,
      });

    tick(1000);

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(result).toEqual({
      authToken: 'test-token',
      participantId: '123',
    });
    expect(repository.login).toHaveBeenCalledWith({
      participantId: '123',
      phoneNumber: '555-5555',
    });
    expect(repository.login).toHaveBeenCalledTimes(1);
  }));
});

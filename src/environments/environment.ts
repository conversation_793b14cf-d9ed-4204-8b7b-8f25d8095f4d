// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: true,
  API_URL: 'https://api.aplazo.mx/',
  APP_NAME: 'APLAZO',
  POS_API_URL: 'https://pos.aplazo.mx',
  POS_WS_URL: 'wss://pos.aplazo.mx',
  HOSTNAME: 'https://posui.aplazo.mx',
  ZENDESK: {
    SDK: 'https://static.zdassets.com/ekr/snippet.js?key=683f9eb6-ad72-47c3-8c33-76d19b1f95f1',
    ID: 'ze-snippet',
  },
  LANDINGPAGE: 'https://aplazo.mx/',
  REGISTER_MERCHANT_URL: 'https://merchant-register.aplazo.mx/',
  CUSTOMER_LOGIN_URL: 'https://customer.aplazo.mx/',
  LOOK_AND_FEEL_CONFIG_URL:
    'https://aplazoassets.s3.us-west-2.amazonaws.com/json/merchants/look-and-feel-migration-config.mx.json',
  GTM_ID: 'GTM-5QDDTK79',
  AUTH_API_URL: 'https://merchant-acs.aplazo.prod/api',
  i18nUrl: 'https://aplazo.github.io/front.in18/premios-aplazo/prod',
  PROMO_API_URL: 'https://mpromotions.aplazo.prod/',
  FEATURE_FLAGS_API_KEY: 'client-N8hvdiyVKZ9iD5KV3SgrpsDXiv6pMUZEIyiPamjmh30',
  FEATURE_FLAGS_ENV: 'production',
  DATADOG: {
    applicationId: 'f31a197e-0666-4347-a42f-b0a086624dfd',
    clientToken: 'pubd5eb0a307a55d032c729375f7e12546c',
    env: 'production',
    service: 'landing-premios-aplazo',
  },
  MERCHANT_DASH_API_URL: 'https://merchantdash.aplazo.mx/',
  WEBCHAT_CONFIG: {
    apiKey:
      'eyJhbGciOiJub25lIn0.eyJvcmciOiI2NGNjMTE4NmUwYTIxZDBiMWQ4Nzc4NTciLCJvcmdOYW1lIjoiYXBsYXpvIiwicG9kIjoicHJvZDEiLCJyb2xlcyI6WyJvcmcudHJhY2tpbmciXX0.',
    brandId: '64cc11899d839023db27aba9',
  },
  PAYMENTS_CORE: {
    apiURL: 'https://ms-payments.aplazo.mx',
  },
  msPaymentMicro: 'https://ms-payments.aplazo.mx/',
  KOUNT: {
    clientID: '100569',
    hostname: 'https://tst.kaptcha.com',
    isSinglePageApp: true,
  },
  msMerchantMicro: 'https://merchant-ms.aplazo.mx',
  UA_URL: 'https://pos.aplazo.mx/api/user_agent/',
  bifrostUrl: 'https://posbifrost.aplazo.net',
  customerRegistrationMicro: 'https://customer-registration.aplazo.mx',
  exposedPublicApiUrl: 'https://core.aplazo.mx',
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.

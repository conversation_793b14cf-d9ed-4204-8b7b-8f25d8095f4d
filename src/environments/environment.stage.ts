export const environment = {
  production: true,
  API_URL: 'https://api.aplazo.net/',
  APP_NAME: 'APLAZO',
  POS_API_URL: 'https://pos.aplazo.net',
  // POS_API_URL: 'http://localhost:8080',
  POS_WS_URL: 'wss://pos.aplazo.net',
  HOSTNAME: 'https://posui.aplazo.net',
  ZENDESK: {
    SDK: 'https://static.zdassets.com/ekr/snippet.js?key=683f9eb6-ad72-47c3-8c33-76d19b1f95f1',
    ID: 'ze-snippet',
  },
  LANDINGPAGE: 'https://aplazo.net/',
  REGISTER_MERCHANT_URL: 'https://merchant-register.aplazo.net/',
  CUSTOMER_LOGIN_URL: 'https://customer.aplazo.net/',
  LOOK_AND_FEEL_CONFIG_URL:
    'https://aplazoassets.s3.us-west-2.amazonaws.com/json/merchants/look-and-feel-migration-config.stg.json',
  GTM_ID: 'GTM-5QDDTK79',
  AUTH_API_URL: 'https://merchant-acs.aplazo.net/api',
  i18nUrl: 'https://aplazo.github.io/front.in18/premios-aplazo/dev',
  PROMO_API_URL: 'https://mpromotions.aplazo.net/',
  FEATURE_FLAGS_API_KEY: 'client-N8hvdiyVKZ9iD5KV3SgrpsDXiv6pMUZEIyiPamjmh30',
  FEATURE_FLAGS_ENV: 'staging',
  DATADOG: {
    applicationId: 'f31a197e-0666-4347-a42f-b0a086624dfd',
    clientToken: 'pubd5eb0a307a55d032c729375f7e12546c',
    env: 'staging',
    service: 'landing-premios-aplazo',
  },
  MERCHANT_DASH_API_URL: 'https://merchantdash.aplazo.net/',
  WEBCHAT_CONFIG: {
    apiKey:
      'eyJhbGciOiJub25lIn0.eyJvcmciOiI2NGNjMTE4NmUwYTIxZDBiMWQ4Nzc4NTciLCJvcmdOYW1lIjoiYXBsYXpvIiwicG9kIjoicHJvZDEiLCJyb2xlcyI6WyJvcmcudHJhY2tpbmciXX0.',
    brandId: '64cc11899d839023db27aba9',
  },
  PAYMENTS_CORE: {
    apiURL: 'https://ms-payments.aplazo.net',
  },
  msPaymentMicro: 'https://ms-payments.aplazo.net/',
  KOUNT: {
    clientID: '100569',
    hostname: 'https://tst.kaptcha.com',
    isSinglePageApp: true,
  },
  msMerchantMicro: 'https://merchant-ms.aplazo.net',
  UA_URL: 'https://pos.aplazo.net/api/user_agent/',
  bifrostUrl: 'https://posbifrost.aplazo.net',
  customerRegistrationMicro: 'https://customer-registration.aplazo.net',
  exposedPublicApiUrl: 'https://core.aplazo.net',
};

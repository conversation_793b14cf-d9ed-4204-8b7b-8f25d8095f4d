import { provideHttpClient } from '@angular/common/http';
import {
  ApplicationConfig,
  importProvidersFrom,
  isDevMode,
} from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { provideMerchantsGTM } from '@aplazo/front-analytics/tag-manager';
import { provideObservability } from '@aplazo/front-observability';
import { provideI18N } from '@aplazo/i18n';
import {
  provideBrowserUtils,
  provideConnectionStatus,
  provideCustomErrorHandler,
  provideJwtDecoder,
  provideLoader,
  provideNotifier,
  provideRedirecter,
  provideTemporal,
  provideUseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { provideScriptRenderer } from '@aplazo/shared-ui/script-renderer';
import { provideServiceWorkerUpdater } from '@aplazo/workers';
import { LogsInitConfiguration } from '@datadog/browser-logs';
import { RumInitConfiguration } from '@datadog/browser-rum';
import { STATSIG_INIT_CONFIG } from '@statsig/angular-bindings';
import { StatsigSessionReplayPlugin } from '@statsig/session-replay';
import { StatsigAutoCapturePlugin } from '@statsig/web-analytics';
import { ToastNoAnimationModule } from 'ngx-toastr';
import packageJson from '../../package.json';
import { routes } from './app.routes';
import { provideAnalyticsService } from './core/config/providers';
import { provideAppRoutes } from './core/domain/config/app-routes.core';
import {
  posEnvironmentCore,
  provideCoreEnvironments,
} from './core/domain/config/pos-env.core';

const rumConfig: RumInitConfiguration = {
  applicationId: posEnvironmentCore.DATADOG_APPLICATION_ID,
  clientToken: posEnvironmentCore.DATADOG_CLIENT_TOKEN,
  env: posEnvironmentCore.DATADOG_ENV,
  sessionSampleRate: posEnvironmentCore.production ? 10 : 0,
  service: 'landing-premios-aplazo',
  traceSampleRate: 100,
  version: packageJson.version,
  allowedTracingUrls: [
    /https:\/\/(api|pos|merchant-acs|merchantdash|mpromotions)\.aplazo\.(mx)/,
  ],
};

const loggerConfig: LogsInitConfiguration = {
  clientToken: posEnvironmentCore.DATADOG_CLIENT_TOKEN,
  sessionSampleRate: posEnvironmentCore.production ? 10 : 0,
  version: packageJson.version,
};

const featureFlagsSettings = {
  sdkKey: posEnvironmentCore.FEATURE_FLAGS_API_KEY,
  user: {
    appVersion: packageJson.version,
    userAgent: navigator.userAgent,
    locale: 'es-MX',
    custom: {
      appName: 'lpa',
    },
  },
  options: {
    environment: {
      tier: posEnvironmentCore.FEATURE_FLAGS_ENV,
    },
    plugins: [new StatsigAutoCapturePlugin(), new StatsigSessionReplayPlugin()],
  },
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideCoreEnvironments(),
    provideRouter(routes),
    provideHttpClient(),
    provideAppRoutes(),
    provideUseCaseErrorHandler(),
    provideLoader(),
    provideNotifier(),
    provideRedirecter(),
    provideAnalyticsService(),
    provideTemporal(),
    provideJwtDecoder(),
    provideScriptRenderer(),
    provideMerchantsGTM({
      apiKey: posEnvironmentCore.GTM_ID,
    }),
    importProvidersFrom(
      ToastNoAnimationModule.forRoot({
        closeButton: true,
        timeOut: 5000,
      })
    ),
    provideI18N({
      remoteUrl: posEnvironmentCore.i18nUrl,
      fallbackLocalUrl: '/assets/i18n',
    }),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideConnectionStatus({
      notifierText: {
        title: 'Sin conexión a internet',
        message:
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      },
      position: 'toast-bottom-left',
    }),
    provideBrowserUtils(),
    provideCustomErrorHandler('chunkFile', 'offlineToastr'),
    provideServiceWorkerUpdater({
      pollInterval: 5 * 1000,
      dialogTitle: 'Actualización disponible',
      dialogMessage:
        'Una nueva versión está disponible. ¿Quieres actualizar ahora?',
    }),
    provideObservability({ rumConfig, loggerConfig }),
    {
      provide: STATSIG_INIT_CONFIG,
      useValue: featureFlagsSettings,
    },
  ],
};

.aplazo-navbar {
  background: var(--color-blue-9);
  padding: 24px 56px 24px 16px;
  height: 96px;
  display: flex;
  justify-content: center;
  width: 100%;
  position: fixed;
  z-index: 3;
  top: 0;
}
@media (min-width: 1310px) {
  .aplazo-navbar {
    padding: 0 25px;
    height: 80px;
  }
}
@media (min-width: 1450px) {
  .aplazo-navbar {
    padding: 0 48px;
  }
}
.aplazo-navbar-active {
  top: 68px;
}
.aplazo-navbar-nav {
  background: inherit;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 1440px;
}
.aplazo-navbar__logo {
  cursor: pointer;
}
.aplazo-navbar__logo img {
  height: 23px;
  height: 23px;
  margin-right: 8px;
}
@media (min-width: 1310px) {
  .aplazo-navbar__logo img {
    width: 128px;
    height: 27px;
  }
}
.aplazo-navbar__options {
  position: relative;
  display: flex;
  align-items: center;
  list-style: none;
}
@media (max-width: 1309px) {
  .aplazo-navbar__options {
    position: absolute;
    margin-left: 0;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    background-color: var(--color-white);
    z-index: 11;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    padding: 80px 16px 0;
  }
  .aplazo-navbar__options button {
    display: block;
  }
}
.aplazo-navbar__options li {
  text-align: center;
}
@media (min-width: 1310px) {
  .aplazo-navbar__options {
    height: 100%;
    margin-left: 35px;
  }
  .aplazo-navbar__options li {
    height: 100%;
    margin-right: 2px;
    display: flex;
    align-items: center;
  }
}
@media (min-width: 1450px) {
  .aplazo-navbar__options {
    margin-left: 66px;
  }
}
.aplazo-navbar__options-link {
  color: var(--color-black-1);
  padding: 20px 0;
  text-decoration: none;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-block;
  position: relative;
  font-weight: 500;
}
@media (min-width: 1310px) {
  .aplazo-navbar__options-link {
    padding: 10px;
    font-size: 16px;
    display: block;
    vertical-align: middle;
    line-height: 20px;
  }
}
.aplazo-navbar__options-link:after {
  background: none repeat scroll 0 0 transparent;
  bottom: 0;
  content: "";
  display: block;
  height: 2px;
  left: 50%;
  position: absolute;
  background: var(--color-black-1);
  transition: width 0.3s ease 0s, left 0.3s ease 0s;
  width: 0;
}
.aplazo-navbar__options-link:hover:after {
  width: 100%;
  left: 0;
}
.aplazo-navbar__options .active-link {
  font-weight: 700;
  cursor: default;
}
.aplazo-navbar__options .active-link:after {
  left: 0;
  width: 100%;
}
.aplazo-navbar__options button {
  display: block;
  margin: 34px 0;
  width: 160px;
  height: 54px;
}
@media (min-width: 1310px) {
  .aplazo-navbar__options button {
    display: none;
  }
}
.aplazo-navbar__options-open {
  transform: translateX(0);
}
.aplazo-navbar__buttons {
  display: flex;
  justify-content: flex-end;
  flex-grow: 2;
}
.aplazo-navbar__buttons button {
  text-transform: initial;
}
@media screen and (max-width: 992px) {
  .aplazo-navbar__buttons button {
    font-size: 11px;
    line-height: 10px;
  }
}
.aplazo-navbar__buttons button:last-of-type {
  margin-left: 8px;
}
@media screen and (min-width: 992px) {
  .aplazo-navbar__buttons button:last-of-type {
    margin-left: 24px;
  }
}
.aplazo-navbar__icon {
  position: absolute;
  top: 40px;
  right: 19px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 18px;
  height: 18px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 12;
}
.aplazo-navbar__icon:focus {
  outline: none;
}
.aplazo-navbar__icon div {
  width: 18px;
  height: 0.15rem;
  background: var(--color-black-1);
  border-radius: 10px;
  transition: all 0.3s linear;
  position: relative;
  transform-origin: 1px;
}
.aplazo-navbar__icon-open div:first-child {
  transform: rotate(45deg);
}
.aplazo-navbar__icon-open div:nth-child(2) {
  opacity: 0;
  transform: translateX(20px);
}
.aplazo-navbar__icon-open div:nth-child(3) {
  transform: rotate(-45deg);
}
@media (min-width: 1310px) {
  .aplazo-navbar__icon {
    display: none;
  }
}
.aplazo-navbar__active-banner {
  top: 68px;
}

.aplazo-button {
  border-radius: 100px;
  border: none;
  font-size: 0.875rem;
  line-height: 1.125rem;
  letter-spacing: 0.5px;
  padding: 17px 28px;
  font-weight: 500;
  color: var(--color-white);
  background: var(--color-black-1);
  cursor: pointer;
  box-sizing: border-box;
}

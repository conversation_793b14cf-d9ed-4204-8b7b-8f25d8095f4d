import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { map } from 'rxjs';
import { APP_ROUTES } from '../../../core/domain/config/app-routes.core';

@Component({
  selector: 'app-navbar',
  imports: [RouterLink, RouterLinkActive, AplazoLogoComponent, AsyncPipe],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
})
export class NavbarComponent {
  readonly #mediaMatcher = inject(AplazoMatchMediaService);
  readonly appRoutes = inject(APP_ROUTES);

  readonly isMobile$ = this.#mediaMatcher.matchSmScreen$.pipe(
    map(m => m?.matches ?? false)
  );

  isOpenMenu = false;
}

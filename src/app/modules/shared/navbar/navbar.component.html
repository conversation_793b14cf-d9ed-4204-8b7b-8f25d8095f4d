<nav class="aplazo-navbar" id="nav">
  <div class="aplazo-navbar-nav">
    <a routerLink="/">
      <aplz-ui-logo
        [size]="(isMobile$ | async) === true ? 'md' : 'lg'"></aplz-ui-logo>
    </a>
    <ul
      class="aplazo-navbar__options"
      [class.aplazo-navbar__options-open]="isOpenMenu">
      <li (click)="isOpenMenu = false">
        <a
          class="aplazo-navbar__options-link"
          [routerLink]="[appRoutes.contestRegistration]"
          routerLinkActive="active-link">
          Inscripción
        </a>
      </li>
      <li (click)="isOpenMenu = false">
        <a
          class="aplazo-navbar__options-link"
          [routerLink]="[appRoutes.contestPosition]"
          routerLinkActive="active-link">
          Mis Registros
        </a>
      </li>
    </ul>
    <div
      class="aplazo-navbar__icon"
      (click)="isOpenMenu = !isOpenMenu"
      [class.aplazo-navbar__icon-open]="isOpenMenu">
      <div></div>
      <div></div>
      <div></div>
    </div>
  </div>
</nav>

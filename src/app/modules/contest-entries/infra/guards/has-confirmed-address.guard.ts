import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { WinnerProfileStore } from '../services/winner-profile-store.service';

export const hasConfirmedAddressGuard: CanActivateFn = () => {
  const store = inject(WinnerProfileStore);
  const router = inject(Router);

  const isAddressConfirmed = store.getIsAddressConfirmed();

  if (!isAddressConfirmed) {
    return router.createUrlTree([
      '/',
      ROUTE_CONFIG.addressConfirmation,
      ROUTE_CONFIG.loginWinner,
    ]);
  }

  return true;
};

import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { WinnerProfileStore } from '../services/winner-profile-store.service';

export const isAddressFormEditionFinishGuard: CanActivateFn = async () => {
  const store = inject(WinnerProfileStore);

  const currentState = await firstValueFrom(store.state$);

  return currentState.finishEditing ?? false;
};

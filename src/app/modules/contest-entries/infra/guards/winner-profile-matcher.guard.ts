import { inject } from '@angular/core';
import { CanMatchFn, Router } from '@angular/router';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { catchError, firstValueFrom } from 'rxjs';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { GetWinnerProfileUseCase } from '../../application/usecases/get-winner-profile.usecase';
import { WinnerProfileStore } from '../services/winner-profile-store.service';

export const winnerProfileMatcher: CanMatchFn = async () => {
  // Winner notifications disabled for MEXP-775
  // Always redirect to main page to disable winner flow
  const router = inject(Router);
  const urlTree = router.createUrlTree(['/']);
  return urlTree;
};

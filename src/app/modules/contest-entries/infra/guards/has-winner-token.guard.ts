import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { firstValueFrom, map } from 'rxjs';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { WinnerProfileStore } from '../services/winner-profile-store.service';

export const hasWinnerTokenGuard: CanActivateFn = async () => {
  // Winner notifications disabled for MEXP-775
  // Always redirect to main page to disable winner flow
  const router = inject(Router);
  const urlTree = router.createUrlTree(['/']);
  return urlTree;
};

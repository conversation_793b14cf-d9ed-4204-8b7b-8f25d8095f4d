import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../core/domain/config/pos-env.core';
import { CityRepositoryResponse } from '../../domain/city';
import {
  ContestRegion,
  ContestRegistrationRequest,
  LoginRequest,
  LoginResponse,
  ParticipantDetailResponse,
  SendQRResponse,
} from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';
import {
  WinnerProfileData,
  WinnerProfileResponse,
} from '../../domain/winner-profile';

@Injectable({ providedIn: 'root' })
export class ContestWithHttpRepository implements ContestRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  retriveLastTyCLink(): Observable<{ termsConditions: string }> {
    return this.#http.get<{ termsConditions: string }>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/campaign/latest-terms-and-conditions`
    );
  }

  retrieveRegions(): Observable<ContestRegion[]> {
    return this.#http.get<ContestRegion[]>(
      `${this.#environment.customerRegistrationMicro}/catalog/states`
    );
  }

  createOne(request: ContestRegistrationRequest): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/sign-up`,
      request
    );
  }

  getOneWithDetails(
    participantCode: string
  ): Observable<ParticipantDetailResponse> {
    return this.#http.get<ParticipantDetailResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/participant-rank/${participantCode}`
    );
  }

  resendParticipantQR(participantId: string): Observable<SendQRResponse> {
    return this.#http.post<SendQRResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/resend-participants-qr`,
      { participantId }
    );
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.#http.post<LoginResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/auth/merchant-rewards/participant/login`,
      credentials
    );
  }

  getCityInfoByZipCode(zipCode: string): Observable<CityRepositoryResponse[]> {
    return this.#http.get<CityRepositoryResponse[]>(
      `${this.#environment.customerRegistrationMicro}/catalog/postalcode/${zipCode}`
    );
  }

  getWinnerProfile(token: string): Observable<WinnerProfileResponse> {
    const headers = new HttpHeaders({
      Authorization: token,
    });

    return this.#http.get<WinnerProfileResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/campaigns/winners/address-confirmation`,
      { headers }
    );
  }

  confirmAddress(request: WinnerProfileData): Observable<void> {
    const { token, ...body } = request;
    const headers = new HttpHeaders({
      Authorization: token ?? '',
    });

    return this.#http.post<void>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/campaigns/winners/address-confirmation`,
      body,
      { headers }
    );
  }

  forgotParticipantCode(phone: number): Observable<void> {
    const params = new HttpParams().set('phone', phone.toString());

    return this.#http.get<void>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/participant`,
      {
        params,
      }
    );
  }
}

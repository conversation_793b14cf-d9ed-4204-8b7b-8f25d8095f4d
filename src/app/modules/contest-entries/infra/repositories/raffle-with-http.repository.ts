import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../core/domain/config/pos-env.core';
import {
  RaffleTicketsRequest,
  RaffleTicketsResponse,
} from '../../domain/raffle';
import { RaffleRepository } from '../../domain/repositories/raffle.repository';

@Injectable({
  providedIn: 'root',
})
export class RaffleWithHttpRepository implements RaffleRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  readonly #url = `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/raffle/raffle-tickets/`;

  getRaffle(request: RaffleTicketsRequest): Observable<RaffleTicketsResponse> {
    let params = new HttpParams();

    if (request.sortBy && request.sortDirection) {
      params = params.set('sortBy', request.sortBy);
      params = params.set('sortDirection', request.sortDirection);
    }

    return this.#http.get<RaffleTicketsResponse>(
      this.#url + request.participantId,
      {
        params,
      }
    );
  }
}

import { inject } from '@angular/core';
import { Route } from '@angular/router';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { GetAllContestsRegions } from '../../application/usecases/retrieve-regions.usecase';
import {
  provideContest,
  provideRaffle,
  withScopedLoader,
} from '../config/providers';
import { hasWinnerTokenGuard } from '../guards/has-winner-token.guard';
import { isAddressFormEditionFinishGuard } from '../guards/is-address-form-edition-finish.guard';
import { winnerProfileMatcher } from '../guards/winner-profile-matcher.guard';
import { AplazoContestEntriesLayoutComponent } from '../layout/contest-entries.layout.component';
import { AplazoContestPositionComponent } from '../pages/contest-position/contest-position.component';
import { AplazoContestRegistrationComponent } from '../pages/contest-registration/contest-registration.component';
import { ForgotParticipantCodeComponent } from '../pages/forgot-participant-code/forgot-participant-code.component';

const contestRoutes: Route[] = [
  {
    path: '',
    component: AplazoContestEntriesLayoutComponent,
    providers: [
      provideContest(withScopedLoader()),
      provideRaffle(withScopedLoader()),
    ],
    children: [
      {
        path: '',
        component: AplazoContestRegistrationComponent,
        resolve: {
          regions: () => inject(GetAllContestsRegions).execute(),
        },
      },
      {
        path: ROUTE_CONFIG.contestRegistration,
        redirectTo: '',
        pathMatch: 'full',
      },
      {
        path: ROUTE_CONFIG.contestPosition,
        component: AplazoContestPositionComponent,
      },
      {
        path: ROUTE_CONFIG.forgotParticipantCode,
        component: ForgotParticipantCodeComponent,
      },
      {
        path: ROUTE_CONFIG.addressConfirmation,
        loadComponent: () =>
          import('../layout/winner-profile.layout').then(
            s => s.WinnerProfileLayoutComponent
          ),
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: ROUTE_CONFIG.loginWinner,
          },
          {
            path: ROUTE_CONFIG.loginWinner,
            loadComponent: () =>
              import('../pages/winner-login/winner-login.component').then(
                s => s.AplazoWinnerLoginComponent
              ),
          },
          {
            path: ROUTE_CONFIG.addressForm,
            canActivate: [hasWinnerTokenGuard],
            canMatch: [winnerProfileMatcher],
            loadComponent: () =>
              import(
                '../pages/address-confirmation-form/address-confirmation-form.component'
              ).then(s => s.AddressConfirmationFormComponent),
          },
          {
            path: ROUTE_CONFIG.confirmedAddress,
            canActivate: [hasWinnerTokenGuard, isAddressFormEditionFinishGuard],
            loadComponent: () =>
              import(
                '../pages/confirmed-address/confirmed-address.component'
              ).then(s => s.ConfirmedAddressComponent),
          },
        ],
      },
    ],
  },
];

export default contestRoutes;

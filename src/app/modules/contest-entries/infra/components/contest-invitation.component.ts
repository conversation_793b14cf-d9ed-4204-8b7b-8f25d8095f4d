import { Component, EventEmitter, inject, Output } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AnalyticsService } from '../../../../core/application/services/analytics.service';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';

@Component({
  selector: 'app-contest-invitation',
  standalone: true,
  template: `
    <article
      class="py-10 px-10 md:px-30 w-full font-light text-dark-secondary text-lg mt-16">
      <div class="max-w-lg mx-auto text-center">
        <h3 class="text-xl font-medium mb-6">¡Gana con Premios APLAZO!</h3>

        <p class="mb-6">
          Inscríbete y participa. Recibe un beneficio por cada nuevo usuario que
          se registre contigo.
        </p>

        <p class="mb-6 font-bold">¿Cómo participar?</p>

        <ol class="mb-6 list-decimal list-inside max-w-sm mx-auto text-center">
          <li class="my-3 text-left">
            <span class="font-bold">Inscríbete ahora</span> presionando el
            botón.
          </li>
          <li class="my-3 text-left">
            <span class="font-bold">Recibe un código QR y un enlace único</span>
            por WhatsApp.
          </li>
          <li class="my-3 text-left">
            <span class="font-bold">Registra nuevos clientes</span> desde esta
            plataforma y recibe un beneficio por cada uno.
          </li>
        </ol>

        <p class="mb-6">¡Es rápido y fácil!</p>
        <p class="mb-6">
          ¡Mientras más clientes registres de forma exitosa, más ganas.
        </p>
        <p class="mb-6">Presiona el botón de abajo y ¡comienza a ganar!</p>

        <blockquote
          class="p-4 my-4 border-s-4 border-dark-tertiary bg-dark-background rounded-lg">
          <p
            class="text-xl italic font-medium leading-relaxed text-dark-secondary">
            Revisa los términos y condiciones para entender más sobre los
            Premios APLAZO.
          </p>
        </blockquote>
        <div class="my-10 flex justify-center">
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="lg"
            [rounded]="true"
            (click)="registrate()">
            ¡Inscríbete ahora!
          </button>
        </div>
      </div>
    </article>
  `,
  imports: [AplazoButtonComponent],
})
export class AplazoContestInvitationComponent {
  readonly #analytics = inject(AnalyticsService);

  @Output()
  registrationEvent = new EventEmitter<void>();

  readonly appRoutes = ROUTE_CONFIG;

  registrate(): void {
    this.registrationEvent.emit();
    this.#analytics.track('customClick', {
      buttonName: 'contestRegistration',
      label: 'contest-entries',
      timestamp: new Date().getTime(),
    });
  }
}

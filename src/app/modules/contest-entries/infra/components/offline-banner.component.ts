import { Component, inject } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { OnlineStatusService } from '../../../../core/services/online-status.service';

@Component({
  selector: 'app-offline-banner',
  standalone: true,
  imports: [AsyncPipe],
  template: `
    @if (!(onlineService.connectionStatus$ | async)?.isOnline) {
      <div class="offline-banner">
        <p>Estás trabajando offline</p>
      </div>
    }
  `,
  styles: [
    `
      .offline-banner {
        background-color: #f44336;
        color: white;
        text-align: center;
        padding: 8px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }
    `,
  ],
})
export class OfflineBannerComponent {
  protected onlineService = inject(OnlineStatusService);
}

import { AsyncPipe } from '@angular/common';
import { Component, EventEmitter, inject, Output } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoaderService, RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  AplazoLowercaseDirective,
  AplazoNoWhiteSpaceDirective,
  AplazoOnlyLettersDirective,
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { iconSpinCircle } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  catchError,
  EMPTY,
  first,
  lastValueFrom,
  map,
  Observable,
  shareReplay,
  take,
  tap,
} from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { CreateOneParticipantUsecase } from '../../../application/usecases/create-one-participant.usecase';
import { ContestRegionUI } from '../../../domain/contest';
import { PREMIOS_APLAZO_ROLES, PremiosAplazoRole } from '../../../domain/roles';

@Component({
  selector: 'app-registration-form',
  templateUrl: './registration-form.component.html',
  imports: [
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
    AplazoLowercaseDirective,
    AplazoNoWhiteSpaceDirective,
    AplazoTrimSpacesDirective,
    AplazoOnlyLettersDirective,
    AplazoLogoComponent,
    ReactiveFormsModule,
    AsyncPipe,
  ],
})
export class AplazoContestRegistrationFormComponent {
  readonly #route = inject(ActivatedRoute);
  readonly #createOne = inject(CreateOneParticipantUsecase);
  readonly #loader = inject(LoaderService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #analytics = inject(AnalyticsService);

  @Output()
  returnInvitationEvent = new EventEmitter<void>();

  @Output()
  moveToPositionEvent = new EventEmitter<void>();

  readonly phoneAsyncErrorKey = 'invalidPhoneAsync';
  readonly SELECTION_LABEL = 'Seleccione una opción';
  readonly roleLabels = PREMIOS_APLAZO_ROLES;

  readonly #asyncError$ = new BehaviorSubject<
    Record<string, ValidationErrors | null>
  >({});
  readonly isLoading$ = this.#loader.isLoading$;

  readonly regions$: Observable<ContestRegionUI> = this.#route.data.pipe(
    map(data => data['regions']),
    take(1),
    shareReplay(1)
  );

  readonly regionsList$ = this.regions$.pipe(
    map(regions => Object.values(regions))
  );

  readonly fullName = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.minLength(2)],
  });

  readonly email = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.email],
  });

  readonly phone = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(10),
      Validators.maxLength(10),
    ],
    asyncValidators: [this.#validatePhoneAsync()],
  });

  readonly region = new FormControl<string>(this.SELECTION_LABEL, {
    nonNullable: true,
    validators: [Validators.required],
    asyncValidators: [
      async (control: AbstractControl): Promise<ValidationErrors | null> => {
        const val = control.value;

        if (val === this.SELECTION_LABEL) {
          return { invalidBadRegion: true };
        }

        const regions = await lastValueFrom(this.regions$);

        const region = regions[val] || null;

        return !region ? { invalidBadRegion: true } : null;
      },
    ],
    updateOn: 'submit',
  });

  readonly role = new FormControl<string>(this.SELECTION_LABEL, {
    nonNullable: true,
    validators: [
      (control: AbstractControl) => {
        const val = control.value;

        if (val === this.SELECTION_LABEL) {
          return null;
        }

        return this.roleLabels.includes(val) ? null : { invalidRole: true };
      },
    ],
    updateOn: 'submit',
  });

  readonly form = new FormGroup({
    fullName: this.fullName,
    email: this.email,
    phone: this.phone,
    region: this.region,
    role: this.role,
  });

  constructor() {
    this.#iconRegister.registerIcons([iconSpinCircle]);
  }

  cancel(): void {
    this.returnInvitationEvent.emit();
  }

  save(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.#createOne
        .execute({
          email: this.email.value,
          fullName: this.fullName.value,
          phone: this.phone.value,
          region: this.region.value,
          role: this.roleLabels.includes(this.role.value as PremiosAplazoRole)
            ? (this.role.value as PremiosAplazoRole)
            : undefined,
        })
        .pipe(
          tap(() => {
            this.#analytics.track('success', {
              category: 'premios_aplazo_landingpage',
              label: 'pa_landingpage_signup_success',
              timestamp: Date.now() as any,
            });
          }),

          tap(() => {
            this.form.reset();
            this.region.setValue(this.SELECTION_LABEL);
            this.role.setValue(this.SELECTION_LABEL);
            this.moveToPositionEvent.emit();
          }),

          catchError(e => {
            if (
              e instanceof RuntimeMerchantError &&
              e.code ===
                'CreateOneParticipantUsecase::execute::alreadyRegistered'
            ) {
              const errors = this.#asyncError$.getValue();
              const phone = this.phone.value;

              errors[phone] = {
                [this.phoneAsyncErrorKey]: e.message,
              };

              this.#asyncError$.next(errors);

              this.phone.updateValueAndValidity();

              return EMPTY;
            }
            return EMPTY;
          })
        )
        .subscribe();
    }
  }

  #validatePhoneAsync(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      const phone = control.value;

      return this.#asyncError$.pipe(
        first(),
        map(stateError => {
          const errors = stateError[phone];

          if (errors) {
            return errors;
          }

          return null;
        })
      );
    };
  }
}

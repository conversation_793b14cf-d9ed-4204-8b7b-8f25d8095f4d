@if ((hasDetails$ | async) === false) {
  <article
    class="py-8 px-6 grid justify-center w-full font-light text-dark-secondary text-lg">
    <div class="max-w-lg">
      <h2 class="text-center text-dark-secondary text-2xl font-medium">
        ¡Consulta tus registros!
      </h2>
      <p
        class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
        Mantente al tanto de tu progreso. Ingresa tu código y accede a tus
        registros y herramientas exclusivas.
      </p>

      <p
        class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
        Ingresa tu
        <span class="mx-1 font-medium"> código de participante </span>
        para consultar tus registros acumulados.
      </p>

      <p
        class="mt-6 leading-8 tracking-normal font-medium text-dark-secondary text-pretty">
        ¿Cómo funciona?
      </p>

      <ol
        class="leading-8 tracking-normal font-light text-dark-secondary text-pretty list-decimal list-inside">
        <li>Encuentra tu código único (Ej. AFIK1234).</li>
        <li>Escríbelo en el campo de búsqueda.</li>
        <li>Haz clic en consultar mis registros para ver tu conteo actual.</li>
      </ol>
    </div>

    <app-participant-code
      [textUI]="textUI"
      [(participantCode)]="search"
      (participantCodeEvent)="retrieveParticipantInfo()"></app-participant-code>

    <a
      *stgCheckGate="'b2b_front_premios_forgot_participant_id'"
      [routerLink]="['/', routes.forgotParticipantCode]"
      aplzButton
      aplzAppearance="basic"
      aplzColor="light"
      size="md">
      ¿Olvidaste tu número de participante?
    </a>
  </article>
} @else {
  <div
    class="px-8 mt-8 flex justify-center md:justify-between items-center flex-wrap gap-4">
    <button
      aplzButton
      (click)="clear()"
      aplzAppearance="stroked"
      aplzColor="aplazo"
      size="md"
      class="flex-grow-0 flex-shrink-0">
      <aplz-ui-icon name="arrow-left" size="md"></aplz-ui-icon>
      <span class="ml-2">Cambiar participante</span>
    </button>

    <p
      class="text-xl text-dark-secondary leading-8 tracking-normal font-light text-right">
      Código Participante:
      <span class="ml-1 font-medium text-lg">
        {{ search() }}
      </span>
    </p>
  </div>
}

import { Async<PERSON>ipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { LoaderService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSpinCircle } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  combineLatest,
  delay,
  finalize,
  lastValueFrom,
  map,
  take,
} from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { SendQRCodeUseCase } from '../../../application/usecases/send-qr-code.usecase';
import { AplazoContestPositionWithSearchComponent } from '../../components/contest-position-with-search/contest-position-with-search.component';
import { ContestStoreService } from '../../services/contest-store.service';

@Component({
  selector: 'app-contest-position',
  template: `
    <div class="pt-20 pb-16 max-w-6xl mx-auto px-4">
      <app-contest-position-with-search></app-contest-position-with-search>

      @if (ranking$ | async; as ranking) {
        @if (ranking.hasDetails) {
          <h2 class="text-xl text-center my-12 text-pretty">
            El reto
            {{ ranking.isFutureDate ? ' termina ' : ' terminó ' }}

            <span class="ml-1">
              {{ ranking.campaignFinishDate }}
            </span>
          </h2>

          <div
            class="flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-12 max-w-6xl mx-auto px-4">
            @if (ranking.data && ranking.data.length === 0) {
              <div
                class="flex flex-col items-center justify-center text-center max-w-md">
                <h2 class="text-lg text-dark-primary text-pretty">
                  No tienes registros
                </h2>
                <p class="font-light text-dark-secondary mt-6 text-pretty">
                  Registra clientes y podrás ser uno de los ganadores de
                  increíbles premios.
                </p>
              </div>

              <!--
            TODO: The Jira Ticket https://aplazo.atlassian.net/browse/MEXP-519 dismiss handling tiers different from '4'
            at least for the moment.
            -->
            } @else {
              <div
                class="flex flex-col items-center justify-center text-center max-w-md mx-auto">
                <h2 class="text-lg text-dark-primary text-pretty">
                  Mis registros acumulados:
                </h2>
                <p class="font-semibold text-dark-primary mt-4 text-4xl">
                  {{ ranking.signupCompletedCount }}
                </p>
              </div>
            }

            <div class="flex flex-col items-center justify-center text-center">
              <h2 class="text-lg text-dark-primary text-pretty mb-4">Mi QR</h2>
              <figure class="w-48 aspect-square mb-6">
                <img
                  class="max-w-full object-contain"
                  [src]="ranking.qrUrl"
                  alt="QR premios Aplazo personalizado por cada participante" />
              </figure>
              <button
                aplzButton
                aplzAppearance="solid"
                aplzColor="dark"
                size="lg"
                [rounded]="true"
                [disabled]="
                  (showSpinner$ | async) === true ||
                  (isThrottled$ | async) === true
                "
                (click)="sendQRCode()">
                <span class="mr-2">{{ buttonText$ | async }}</span>
                @if (showSpinner$ | async) {
                  <aplz-ui-icon name="spin-circle" size="sm"></aplz-ui-icon>
                }
              </button>
            </div>
          </div>
        }
      }
    </div>
  `,
  imports: [
    AsyncPipe,
    AplazoContestPositionWithSearchComponent,
    AplazoButtonComponent,
    AplazoIconComponent,
  ],
})
export class AplazoContestPositionComponent implements OnInit {
  readonly #store = inject(ContestStoreService);
  readonly #analytics = inject(AnalyticsService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #loader = inject(LoaderService);
  readonly #sendQRUseCase = inject(SendQRCodeUseCase);
  readonly ranking$ = this.#store.contestRanking$.pipe(
    map(ranking => {
      if (!ranking || !ranking.data) {
        return { ...ranking, signupCompletedCount: 0 };
      }
      const rankItem = ranking.data.find(item => item.matched);
      const signupCompletedCount = rankItem ? rankItem.totalRegistration : 0;
      return { ...ranking, signupCompletedCount };
    })
  );

  readonly #isThrottled$ = new BehaviorSubject<boolean>(false);
  readonly isThrottled$ = this.#isThrottled$.asObservable();

  readonly #isLoading$ = this.#loader.isLoading$;

  THROTTLE_TIME = 10000;

  readonly showSpinner$ = combineLatest([
    this.#isLoading$,
    this.isThrottled$,
  ]).pipe(map(([isLoading, isThrottled]) => isLoading || isThrottled));

  readonly isDisabled$ = this.showSpinner$;

  readonly buttonText$ = this.showSpinner$.pipe(
    map(isSpinning => (isSpinning ? 'Enviando QR...' : 'Enviar QR'))
  );

  constructor() {
    this.#iconRegister.registerIcons([iconSpinCircle]);
  }

  ngOnInit(): void {
    this.#analytics.track('pageView', {
      timestamp: Date.now() as any,
    });
  }

  async sendQRCode(): Promise<void> {
    const isDisabled = await lastValueFrom(this.isDisabled$.pipe(take(1)));

    if (isDisabled) {
      return;
    }

    this.#isThrottled$.next(true);
    this.#sendQRUseCase
      .execute()
      .pipe(
        delay(this.THROTTLE_TIME),
        finalize(() => {
          this.#isThrottled$.next(false);
        })
      )
      .subscribe();
  }
}

import { Component, inject, signal } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconArrowLeft, iconEnvelope } from '@aplazo/ui-icons';
import { catchError, EMPTY, tap } from 'rxjs';
import { APP_ROUTES } from '../../../../../core/domain/config/app-routes.core';
import { SendParticipantIdUsecase } from '../../../application/usecases/send-participant-id.usecase';

@Component({
  template: `
    <div
      class="min-h-dvh w-full bg-[#fcfcfc] flex flex-col items-center justify-start pt-36">
      <article>
        <div class="min-h-12 mb-4 flex items-center">
          <a
            [routerLink]="['/', routes.contestPosition]"
            aplzButton
            aplzAppearance="basic"
            aplzColor="light"
            size="md">
            <aplz-ui-icon name="arrow-left" size="md" />
            <span class="ml-2">Volver a inicio</span>
          </a>
        </div>
        <aplz-ui-card size="md">
          <div class="grid place-items-center p-4">
            <button
              aplzButton
              aplzColor="info"
              aplzAppearance="solid"
              size="md"
              [rounded]="true"
              class="pointer-events-none p-4">
              <aplz-ui-icon name="envelope" size="md" />
            </button>
          </div>
          <h1
            class="text-center text-2xl mb-10 font-bold text-special-info text-pretty">
            ¿Olvidaste tu número de participante?
          </h1>
          <p class="text-center text-lg text-dark-secondary">
            Si has olvidado tu código de participante, puedes recuperarlo
            ingresando tu número de teléfono.
          </p>
          <form #form="ngForm" class="mt-12" (ngSubmit)="sendCode(form)">
            <aplz-ui-form-field>
              <input
                type="text"
                name="phone"
                inputmode="tel"
                placeholder="55-1234-5678"
                aplzFormInput
                required
                aplazoOnlyNumbers
                [ngModel]="phone()"
                (ngModelChange)="phone.set($event)"
                [aplazoTruncateLength]="MAX_PHONE_LENGTH"
                [minlength]="MAX_PHONE_LENGTH"
                [maxlength]="MAX_PHONE_LENGTH"
                ngModel
                #phoneInput="ngModel" />
              <aplz-ui-form-label>Número de teléfono</aplz-ui-form-label>

              <ng-container aplzFormError>
                @if (phoneInput.touched && phoneInput.hasError('required')) {
                  <p class="text-special-danger">
                    El número de teléfono es requerido
                  </p>
                } @else if (
                  phoneInput.touched && phoneInput.hasError('minlength')
                ) {
                  <p class="text-special-danger">
                    El número de teléfono debe tener
                    {{ MAX_PHONE_LENGTH }} dígitos
                  </p>
                }
              </ng-container>
            </aplz-ui-form-field>

            <div class="flex mt-10">
              <button
                aplzButton
                aplzAppearance="solid"
                aplzColor="info"
                size="md"
                [fullWidth]="true"
                [disabled]="phoneInput.pristine">
                @if (phoneInput.pristine) {
                  Ingresar número de teléfono
                } @else {
                  Enviar código de participante
                }
              </button>
            </div>
          </form>

          <p class="text-center text-sm text-dark-secondary mt-10">
            ¿Recordaste tu código de participante?
            <a
              [routerLink]="['/', routes.contestPosition]"
              class="text-special-info">
              Inicia sesión
            </a>
          </p>
        </aplz-ui-card>
      </article>
    </div>
  `,
  imports: [
    FormsModule,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
    RouterLink,
  ],
})
export class ForgotParticipantCodeComponent {
  readonly #router = inject(Router);
  readonly #icons = inject(AplazoIconRegistryService);
  readonly #sendParticipantIdUsecase = inject(SendParticipantIdUsecase);

  readonly routes = inject(APP_ROUTES);
  readonly MAX_PHONE_LENGTH = 10;
  readonly phone = signal<string | null>(null);

  constructor() {
    this.#icons.registerIcons([iconEnvelope, iconArrowLeft]);
  }

  sendCode(form: NgForm): void {
    form.form.markAllAsTouched();

    if (form.invalid) {
      return;
    }

    this.#sendParticipantIdUsecase
      .execute(Number(this.phone()))
      .pipe(
        tap(() => {
          this.#router.navigate(['/', this.routes.contestPosition]);

          form.reset();
        }),
        catchError(() => EMPTY)
      )
      .subscribe();
  }
}

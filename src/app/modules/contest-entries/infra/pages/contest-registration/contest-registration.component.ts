import { Async<PERSON>ipe, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BehaviorSubject, map } from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { ROUTE_CONFIG } from '../../../../../core/domain/config/app-routes.core';
import { AplazoContestInvitationComponent } from '../../components/contest-invitation.component';
import { AplazoContestRegistrationFormComponent } from '../../components/registration/registration-form.component';

export const DYNAMIC_CONTEST_REGISTRATION_COMPONENT = {
  invitation: AplazoContestInvitationComponent,
  registration: AplazoContestRegistrationFormComponent,
} as const;

export type DynamicContestComponentKey =
  keyof typeof DYNAMIC_CONTEST_REGISTRATION_COMPONENT;
export type DynamicContestComponent =
  (typeof DYNAMIC_CONTEST_REGISTRATION_COMPONENT)[DynamicContestComponentKey];

@Component({
  selector: 'app-contest-registration',
  template: `
    <ng-container [ngTemplateOutlet]="component$ | async"></ng-container>

    <ng-template #registrationForm>
      <app-registration-form
        (returnInvitationEvent)="change('invitation')"
        (moveToPositionEvent)="change('position')"></app-registration-form>
    </ng-template>

    <ng-template #invitation>
      <app-contest-invitation
        (registrationEvent)="change('registration')"></app-contest-invitation>
    </ng-template>
  `,
  imports: [
    NgTemplateOutlet,
    AsyncPipe,
    AplazoContestRegistrationFormComponent,
    AplazoContestInvitationComponent,
  ],
})
export class AplazoContestRegistrationComponent implements OnInit {
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #analytics = inject(AnalyticsService);

  @ViewChild('registrationForm', { read: TemplateRef, static: true })
  _registrationFormTemplate!: TemplateRef<unknown>;

  @ViewChild('invitation', { read: TemplateRef, static: true })
  _invitationTemplate!: TemplateRef<unknown>;

  readonly componentKey$ =
    new BehaviorSubject<DynamicContestComponentKey | null>('invitation');

  readonly component$ = this.componentKey$.pipe(
    map(key => {
      if (key === 'registration') {
        return this._registrationFormTemplate;
      }

      return this._invitationTemplate;
    })
  );

  change(key: 'registration' | 'invitation' | 'position'): void {
    if (key === 'position') {
      this.#router.navigate(['..', ROUTE_CONFIG.contestPosition], {
        relativeTo: this.#route,
      });
      return;
    }

    if (key === 'registration') {
      this.#analytics.track('buttonClick', {
        buttonName: 'initRegistrationButton',
        timestamp: new Date().getTime(),
      });
    }

    this.componentKey$.next(key);
  }

  ngOnInit(): void {
    this.#analytics.track('pageView', {
      timestamp: new Date().getTime(),
    });
  }
}

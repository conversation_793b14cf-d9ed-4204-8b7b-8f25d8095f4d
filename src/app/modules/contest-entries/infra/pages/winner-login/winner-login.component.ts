import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  AsyncValidatorFn,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  AplazoNoWhiteSpaceDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { iconSpinCircle } from '@aplazo/ui-icons';
import { StatsigService } from '@statsig/angular-bindings';
import { BehaviorSubject, catchError, EMPTY, first, map, tap } from 'rxjs';
import { ROUTE_CONFIG } from '../../../../../core/domain/config/app-routes.core';
import { DoWinnerLoginUseCase } from '../../../application/usecases/do-winner-login.usecase';
import { WinnerProfileStore } from '../../services/winner-profile-store.service';

@Component({
  selector: 'app-winner-login',
  templateUrl: './winner-login.component.html',
  imports: [
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
    AplazoNoWhiteSpaceDirective,
    AplazoLogoComponent,
    ReactiveFormsModule,
    AsyncPipe,
  ],
})
export class AplazoWinnerLoginComponent {
  readonly #loader = inject(LoaderService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #loginUseCase = inject(DoWinnerLoginUseCase);
  readonly #winnerProfile = inject(WinnerProfileStore);
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #flags = inject(StatsigService);

  readonly #loginError$ = new BehaviorSubject<boolean>(false);
  readonly loginError$ = this.#loginError$.pipe(
    map<boolean, ValidationErrors | null>(e => {
      if (e) {
        return {
          invalidCredentials: true,
        };
      }

      return null;
    })
  );

  readonly loader$ = this.#loader.isLoading$.pipe(map(l => ({ isLoading: l })));

  readonly phoneNumber = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(10),
      Validators.maxLength(10),
    ],
    asyncValidators: [this.#validateAsync()],
  });

  readonly participantId = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.minLength(4)],
    asyncValidators: [this.#validateAsync()],
  });

  readonly form = new FormGroup({
    participantId: this.participantId,
    phoneNumber: this.phoneNumber,
  });

  constructor() {
    this.#iconRegister.registerIcons([iconSpinCircle]);
  }

  save(): void {
    this.#loginError$.next(false);
    this.form.markAllAsTouched();

    if (
      this.form.valid ||
      (this.participantId.hasError('invalidCredentials') &&
        this.phoneNumber.hasError('invalidCredentials'))
    ) {
      this.#loginUseCase
        .execute({
          participantId: this.participantId.value,
          phoneNumber: this.phoneNumber.value,
        })
        .pipe(
          tap(({ authToken }) => {
            this.#winnerProfile.setToken(authToken);
            this.#winnerProfile.setParticipantId(this.participantId.value);
          }),
          tap(() => {
            this.#flags.logEvent(
              'lpa_front_winner_login_success',
              this.phoneNumber.value,
              {
                phone_number: this.phoneNumber.value,
                participant_id: this.participantId.value,
              }
            );
          }),
          catchError(() => {
            this.#loginError$.next(true);
            this.#flags.logEvent(
              'lpa_front_winner_login_error',
              this.phoneNumber.value,
              {
                phone_number: this.phoneNumber.value,
              }
            );

            this.phoneNumber.updateValueAndValidity();
            this.participantId.updateValueAndValidity();

            this.#winnerProfile.clearState();

            return EMPTY;
          }),
          tap(() => {
            this.form.reset();

            this.#router.navigate(['..', ROUTE_CONFIG.addressForm], {
              relativeTo: this.#route,
            });
          })
        )
        .subscribe();
    }
  }

  #validateAsync(): AsyncValidatorFn {
    return () => {
      return this.loginError$.pipe(first());
    };
  }
}

@if(loader$ | async; as loader) {
<div class="flex flex-col items-center mt-24 mb-16">
  <aplz-ui-logo size="md"></aplz-ui-logo>
</div>

<form [formGroup]="form" class="w-full max-w-sm mx-auto" (ngSubmit)="save()">
  <aplz-ui-form-field>
    <aplz-ui-form-label>ID del Participante</aplz-ui-form-label>
    <input
      aplzFormInput
      formControlName="participantId"
      type="text"
      id="participant-id"
      aplazoNoWhiteSpace
      placeholder="Introduce tu ID de participante"
    />
    @if (participantId.touched &&( participantId.hasError('required') ||
    participantId.hasError('minlength') ||
    participantId.hasError('invalidCredentials'))) {
    <p aplzFormError>Por favor introduce un ID de participante válido.</p>
    }
  </aplz-ui-form-field>

  <aplz-ui-form-field>
    <aplz-ui-form-label>Número Telefónico</aplz-ui-form-label>
    <input
      aplzFormInput
      aplazoOnlyNumbers
      [aplazoTruncateLength]="10"
      formControlName="phoneNumber"
      type="text"
      inputMode="tel"
      id="phone"
      placeholder="Introduce tu número telefónico"
    />
    @if (phoneNumber.touched && ( phoneNumber.hasError('required') ||
    phoneNumber.hasError('minlength') || phoneNumber.hasError('maxlength') ||
    phoneNumber.hasError('invalidCredentials'))) {
    <p aplzFormError>Por favor introduce un número de teléfono válido.</p>
    }
  </aplz-ui-form-field>

  <div class="flex gap-6 flex-wrap justify-end">
    <button
      aplzButton
      type="submit"
      aplzAppearance="solid"
      aplzColor="dark"
      size="md"
      [disabled]="loader.isLoading"
    >
      <span class="mr-2">
        {{ loader.isLoading ? "Enviando..." : "Enviar" }}
      </span>
      @if (loader.isLoading) {
      <aplz-ui-icon name="spin-circle" size="sm" [spin]="true"></aplz-ui-icon>
      }
    </button>
  </div>
</form>
}

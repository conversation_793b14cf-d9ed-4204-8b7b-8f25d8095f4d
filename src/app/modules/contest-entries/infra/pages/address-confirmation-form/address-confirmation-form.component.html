<main class="w-full max-w-2xl mx-auto mt-32">
  <blockquote class="mb-6 px-8 md:border-l-4 border-dark-secondary">
    <p class="text-lg font-medium my-6">📢 Importante:</p>
    <p class="text-md text-dark-secondary leading-8">
      Tienes 10 días para completar este formulario.
    </p>
    <p class="text-md text-dark-secondary leading-8">
      Si la dirección prellenada es correcta, simplemente haz clic en
      <span class="font-bold mx-1"> 'Confirmar Dirección' </span>
      sin modificarla y mantendremos tu información actual.
    </p>
    <p class="text-md text-dark-secondary leading-8">
      Si realizas cambios en la dirección y confirmas el envío, no podrás
      actualizarla nuevamente.
    </p>
  </blockquote>

  <section class="max-w-xl mx-auto mt-12 mb-10">
    <form
      [formGroup]="addressForm"
      class="w-full min-h-96 px-4"
      (ngSubmit)="confirmAddress()">
      <div
        class="grid grid-cols-1 md:grid-cols-2 md:grid-rows-2 gap-y-2 gap-x-4">
        <aplz-ui-form-field class="col-span-1">
          <aplz-ui-form-label>ID del Participante</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="participantId"
            type="text" />
        </aplz-ui-form-field>
        <aplz-ui-form-field class="col-span-full">
          <aplz-ui-form-label>Nombre Completo</aplz-ui-form-label>
          <input
            aplzFormInput
            aplazoTrimSpaces
            formControlName="name"
            type="text" />
          @if (
            fullNameControl.touched && fullNameControl.hasError('required')
          ) {
            <p aplzFormError>El nombre completo es requerido</p>
          }
        </aplz-ui-form-field>
      </div>

      @if (address()) {
        <aplz-ui-card class="mb-10 hover:bg-dark-background" appearance="flat">
          <h3 class="text-lg font-bold mb-4">Dirección Registrada</h3>
          <div class="text-pretty text-md text-dark-secondary">
            {{ address() }}
          </div>
        </aplz-ui-card>
      }

      <article
        [class.aplz-address-form--hidden]="!showAddressForm()"
        [class.aplz-address-form]="showAddressForm()">
        <div
          class="grid grid-cols-1 md:grid-cols-2 md:grid-rows-2 gap-y-2 gap-x-4">
          <aplz-ui-form-field class="col-span-full">
            <aplz-ui-form-label>Calle</aplz-ui-form-label>
            <input
              aplzFormInput
              aplazoTrimSpaces
              formControlName="street"
              id="street"
              type="text" />
            @if (streetControl.touched && streetControl.hasError('required')) {
              <p aplzFormError>La calle es requerida</p>
            }
          </aplz-ui-form-field>
          <aplz-ui-form-field>
            <aplz-ui-form-label>Número Exterior</aplz-ui-form-label>
            <input
              aplzFormInput
              aplazoTrimSpaces
              formControlName="exteriorNumber"
              [aplazoTruncateLength]="MAX_CHARACTERS_FOR_NUMBER"
              type="text" />
            @if (
              exteriorNumberControl.touched &&
              exteriorNumberControl.hasError('required')
            ) {
              <p aplzFormError>El número exterior es requerido</p>
            }
            @if (
              exteriorNumberControl.touched &&
              exteriorNumberControl.hasError('maxlength')
            ) {
              <p aplzFormError>
                El número exterior no puede exceder los 10 dígitos
              </p>
            }
          </aplz-ui-form-field>
          <aplz-ui-form-field>
            <aplz-ui-form-label>Número Interior</aplz-ui-form-label>
            <input
              aplzFormInput
              aplazoTrimSpaces
              formControlName="interiorNumber"
              [aplazoTruncateLength]="MAX_CHARACTERS_FOR_NUMBER"
              type="text" />

            @if (
              interiorNumberControl.touched &&
              interiorNumberControl.hasError('maxlength')
            ) {
              <p aplzFormError>
                El número interior no puede exceder los 10 dígitos
              </p>
            }
          </aplz-ui-form-field>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4">
          <aplz-ui-form-field>
            <aplz-ui-form-label>Código Postal</aplz-ui-form-label>
            <input
              aplzFormInput
              aplazoOnlyNumbers
              [aplazoTruncateLength]="5"
              formControlName="postalCode"
              type="text" />

            <!-- required -->
            @if (
              zipCodeControl.touched && zipCodeControl.hasError('required')
            ) {
              <p aplzFormError>El código postal es requerido</p>
            }

            <!-- minlength and maxlength -->
            @if (
              zipCodeControl.touched &&
              !zipCodeControl.hasError('required') &&
              (zipCodeControl.hasError('minlength') ||
                zipCodeControl.hasError('maxlength'))
            ) {
              <p aplzFormError>El código postal debe tener 5 dígitos</p>
            }
          </aplz-ui-form-field>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4">
          <!-- for suburbs -->
          @if (suburbs$ | async; as suburbs) {
            <!-- for a select or simple input -->
            @if (suburbs.length <= 1) {
              <aplz-ui-form-field class="col-span-1">
                <aplz-ui-form-label>Colonia</aplz-ui-form-label>
                <input
                  aplzFormInput
                  aplazoTrimSpaces
                  formControlName="neighborhood"
                  type="text" />
                @if (
                  neighborhoodControl.touched &&
                  neighborhoodControl.hasError('required')
                ) {
                  <p aplzFormError>La colonia es requerida</p>
                }
              </aplz-ui-form-field>
            } @else if (suburbs.length > 1) {
              <aplz-ui-form-field>
                <aplz-ui-form-label>Colonia</aplz-ui-form-label>
                <select aplzFormSelect formControlName="neighborhood">
                  @for (item of suburbs; track item) {
                    <option [value]="item">{{ item }}</option>
                  }
                  <option [value]="OVERRIDER_VALUE">Otro</option>
                </select>
              </aplz-ui-form-field>
            }
            <!-- end of suburbs nested conditional -->
          }

          <!-- for municipalities -->
          @if (municipalities$ | async; as municipalities) {
            <!-- for a select or simple input -->
            @if (municipalities.length <= 1) {
              <aplz-ui-form-field class="col-span-1">
                <aplz-ui-form-label>Municipio</aplz-ui-form-label>
                <input
                  aplzFormInput
                  aplazoTrimSpaces
                  formControlName="municipality"
                  type="text" />
                @if (
                  municipalityControl.touched &&
                  municipalityControl.hasError('required')
                ) {
                  <p aplzFormError>El municipio es requerido</p>
                }
              </aplz-ui-form-field>
            } @else if (municipalities.length > 1) {
              <aplz-ui-form-field>
                <aplz-ui-form-label>Municipio</aplz-ui-form-label>
                <select aplzFormSelect formControlName="municipality">
                  @for (item of municipalities; track item) {
                    <option [value]="item">{{ item }}</option>
                  }
                  <option [value]="OVERRIDER_VALUE">Otro</option>
                </select>
              </aplz-ui-form-field>
            }
            <!-- end of municipalities nested conditional -->
          }

          <!-- for states -->
          @if (states$ | async; as states) {
            <!-- for a select or simple input -->
            @if (states.length <= 1) {
              <aplz-ui-form-field class="col-span-1">
                <aplz-ui-form-label>Estado</aplz-ui-form-label>
                <input
                  aplzFormInput
                  aplazoTrimSpaces
                  formControlName="state"
                  type="text" />
                @if (
                  stateControl.touched && stateControl.hasError('required')
                ) {
                  <p aplzFormError>El estado es requerido</p>
                }
              </aplz-ui-form-field>
            } @else if (states.length > 1) {
              <aplz-ui-form-field>
                <aplz-ui-form-label>Estado</aplz-ui-form-label>
                <select aplzFormSelect formControlName="state">
                  @for (item of states; track item) {
                    <option [value]="item">{{ item }}</option>
                  }
                  <option [value]="OVERRIDER_VALUE">Otro</option>
                </select>
              </aplz-ui-form-field>
            }
            <!-- end of states nested conditional -->
          }
        </div>

        <fieldset
          class="border border-solid border-dark-secondary rounded-lg px-4 py-8">
          <legend class="text-md text-dark-primary text-center">
            Cuéntanos más sobre tu ubicación para asegurar que tu entrega llegue
            sin problemas.
          </legend>
          <div class="mb-8">
            <label
              class="inline-flex items-center"
              [class.cursor-pointer]="!isMallControl.disabled"
              [class.cursor-not-allowed]="isMallControl.disabled">
              <input
                type="checkbox"
                class="sr-only peer"
                formControlName="isMall" />
              <div
                class="relative w-14 h-7 bg-dark-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-special-info/10 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-light-primary after:content-[''] after:absolute after:top-0.5 after:start-[4px] after:bg-light-primary after:border-dark-tertiary after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-special-info"></div>
              <p class="flex items-baseline">
                <span class="ms-3 text-sm text-dark-primary">
                  ¿Estás dentro de una plaza?
                </span>

                <span class="ml-2">
                  {{ isMallControl.value ? 'Sí' : 'No' }}
                </span>
              </p>
            </label>
          </div>

          <div class="mb-8 relative">
            <label
              for="message"
              class="text-sm"
              [class.text-special-danger]="
                referenceControl.touched &&
                referenceControl.hasError('maxlength')
              "
              [class.text-dark-tertiary]="
                !referenceControl.touched ||
                !referenceControl.hasError('maxlength')
              ">
              Referencia de ubicación
              <span
                class="text-md ml-3 tabular-nums"
                [class.text-dark-tertiary]="!caractersLeft()?.hasError"
                [class.text-special-danger]="caractersLeft()?.hasError">
                {{ caractersLeft()?.value }}
              </span>
            </label>
            <textarea
              formControlName="reference"
              id="message"
              rows="4"
              class="block p-2.5 w-full text-md text-dark-primary bg-transparent rounded-lg border focus:ring-4 focus:ring-special-info/10 focus-visible:outline-none"
              [class.focus-visible:border-special-danger]="
                referenceControl.hasError('maxlength')
              "
              [class.border-special-danger]="
                referenceControl.hasError('maxlength')
              "
              [class.border-dark-secondary]="
                !referenceControl.hasError('maxlength') &&
                !referenceControl.disabled
              "
              [class.border-dark-tertiary]="referenceControl.disabled"
              [class.cursor-not-allowed]="referenceControl.disabled"
              [class.focus-visible:ring-special-danger]="
                referenceControl.hasError('maxlength')
              "
              placeholder="Escribe una referencia de ubicación. Por ejemplo: Cerca de la entrada principal... Entre calle 1 y calle 2..."></textarea>
            @if (
              referenceControl.touched && referenceControl.hasError('maxlength')
            ) {
              <span class="text-special-danger text-sm absolute -bottom-5 pl-2">
                El campo de referencia no puede exceder los 255 caracteres.
              </span>
            }
          </div>
        </fieldset>
      </article>
      <div class="flex justify-end gap-4 mt-12">
        @if (this.showUpdateAddress()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="light"
            size="md"
            (click)="openForm()"
            [disabled]="(isLoading$ | async) === true">
            <span class="mr-2"> Modificar Dirección </span>
            @if ((isLoading$ | async) === true) {
              <aplz-ui-icon
                name="spin-circle"
                size="sm"
                [spin]="true"></aplz-ui-icon>
            }
          </button>
        } @else if (this.showRollbackAddress()) {
          <button
            aplzButton
            type="button"
            aplzAppearance="stroked"
            aplzColor="info"
            size="md"
            (click)="rollbackAddress()"
            [disabled]="(isLoading$ | async) === true">
            <span class="mr-2"> Restablecer Dirección </span>

            @if ((isLoading$ | async) === true) {
              <aplz-ui-icon
                name="spin-circle"
                size="sm"
                [spin]="true"></aplz-ui-icon>
            }
          </button>
        }

        <button
          aplzButton
          type="submit"
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          [disabled]="(isLoading$ | async) === true">
          <span class="mr-2">
            {{
              (isLoading$ | async) === true
                ? 'Enviando...'
                : 'Confirmar Dirección'
            }}
          </span>
          @if ((isLoading$ | async) === true) {
            <aplz-ui-icon
              name="spin-circle"
              size="sm"
              [spin]="true"></aplz-ui-icon>
          }
        </button>
      </div>
    </form>
  </section>
</main>

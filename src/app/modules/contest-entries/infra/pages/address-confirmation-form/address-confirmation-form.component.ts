import { AsyncPipe } from '@angular/common';
import {
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { iconSpinCircle } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import {
  BehaviorSubject,
  combineLatest,
  combineLatestWith,
  debounceTime,
  defer,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  map,
  MonoTypeOperatorFunction,
  of,
  pipe,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  takeUntil,
  tap,
  timer,
  withLatestFrom,
} from 'rxjs';
import { ROUTE_CONFIG } from '../../../../../core/domain/config/app-routes.core';
import { ConfirmAddressUsecase } from '../../../application/usecases/confirm-address.usecase';
import { GetCityInfoUsecase } from '../../../application/usecases/get-city-info.usecase';
import { CityUI } from '../../../domain/city';
import { WinnerProfileData } from '../../../domain/winner-profile';
import { WinnerProfileStore } from '../../services/winner-profile-store.service';

@Component({
  selector: 'app-address-confirmation-form',
  imports: [
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
    AplazoTrimSpacesDirective,
    AplazoCardComponent,
    ReactiveFormsModule,
    AsyncPipe,
    AplazoIconComponent,
  ],
  templateUrl: './address-confirmation-form.component.html',
  styles: `
    .aplz-address-form--hidden {
      height: 0;
      overflow: hidden;
    }
    .aplz-address-form {
      height: auto;
      overflow: visible;
      transition: height 0.3s ease-in-out;
    }
  `,
})
export class AddressConfirmationFormComponent implements OnInit, OnDestroy {
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #store = inject(WinnerProfileStore);
  readonly #cityInfo = inject(GetCityInfoUsecase);
  readonly #dialog = inject(DialogService);
  readonly #confirmAddress = inject(ConfirmAddressUsecase);
  readonly #router = inject(Router);
  readonly #loader = inject(LoaderService);

  readonly #destroy = new Subject<void>();

  readonly OVERRIDER_VALUE = '=##=unknown=#=##=';
  readonly DEBOUNCE_TIME = 450;
  readonly MAX_REFERENCE_LENGTH = 255;
  readonly MAX_CHARACTERS_FOR_NUMBER = 10;

  readonly isLoading$ = this.#loader.isLoading$;

  readonly participantIdControl = new FormControl<string>(
    {
      value: this.#store.getAddressWithParticipant().participantId || '',
      disabled: true,
    },
    {
      nonNullable: true,
      validators: [Validators.required],
    }
  );
  readonly fullNameControl = new FormControl<string>(
    {
      value: this.#store.getAddressWithParticipant().name || '',
      disabled: Boolean(this.#store.getAddressWithParticipant().name),
    },
    {
      nonNullable: true,
      validators: [Validators.required],
    }
  );
  readonly streetControl = new FormControl<string>(
    this.#store.getAddressWithParticipant().street || '',
    {
      nonNullable: true,
      validators: [Validators.required],
    }
  );
  readonly exteriorNumberControl = new FormControl<string>(
    this.#store.getAddressWithParticipant().exteriorNumber || '',
    {
      nonNullable: true,
      validators: [Validators.required, Validators.maxLength(10)],
    }
  );
  readonly interiorNumberControl = new FormControl<string>(
    this.#store.getAddressWithParticipant().interiorNumber || '',
    {
      nonNullable: false,
      validators: [Validators.maxLength(10)],
    }
  );
  readonly zipCodeControl = new FormControl<string>(
    this.#store.getAddressWithParticipant().postalCode || '',
    {
      nonNullable: true,
      validators: [
        Validators.required,
        Validators.maxLength(5),
        Validators.minLength(5),
      ],
    }
  );
  readonly neighborhoodControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly municipalityControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly stateControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });
  readonly isMallControl = new FormControl<boolean>(
    this.#store.getAddressWithParticipant().isMall ?? false
  );
  readonly referenceControl = new FormControl<string>(
    this.#store.getAddressWithParticipant().reference ?? '',
    {
      nonNullable: false,
      validators: [Validators.maxLength(this.MAX_REFERENCE_LENGTH)],
    }
  );

  readonly caractersLeft = toSignal(
    this.referenceControl.valueChanges.pipe(
      startWith(this.referenceControl.value),
      map(val => {
        const length = val?.length ?? 0;
        const maxLength = this.MAX_REFERENCE_LENGTH;
        const remaining = maxLength - length;
        const truncateToZero = remaining < 0 ? 0 : remaining;

        return {
          value: `${truncateToZero} / ${maxLength}`,
          hasError: length > maxLength,
        };
      })
    )
  );

  readonly addressForm = new FormGroup({
    participantId: this.participantIdControl,
    name: this.fullNameControl,
    street: this.streetControl,
    exteriorNumber: this.exteriorNumberControl,
    interiorNumber: this.interiorNumberControl,
    postalCode: this.zipCodeControl,
    neighborhood: this.neighborhoodControl,
    municipality: this.municipalityControl,
    state: this.stateControl,
    isMall: this.isMallControl,
    reference: this.referenceControl,
  });

  readonly #debouncer = (
    time: number,
    initialValue?: string
  ): MonoTypeOperatorFunction<string | null> => {
    return pipe(
      debounceTime(time),
      startWith(initialValue ?? null),
      distinctUntilChanged()
    );
  };

  readonly zipCode$ = this.zipCodeControl.valueChanges.pipe(
    this.#debouncer(this.DEBOUNCE_TIME, this.zipCodeControl.value),
    filter(
      () =>
        this.zipCodeControl.status === 'VALID' || this.zipCodeControl.pristine
    ),
    tap(() => {
      this.#stateOverrider$.next(false);
      this.#municipalityOverrider$.next(false);
      this.#suburbOverrider$.next(false);
    }),
    takeUntil(this.#destroy),
    shareReplay(1)
  );

  readonly cityinfo$ = this.zipCode$.pipe(
    withLatestFrom(this.#store.state$),
    switchMap(([zipCode, state]) => {
      const hasPrefilledAddress = Boolean(state.postalCode);
      const zipCodeHasChanged = zipCode !== state.postalCode;

      if (!zipCode || (hasPrefilledAddress && !zipCodeHasChanged)) {
        return of([]);
      }

      return this.#cityInfo.execute(zipCode);
    }),
    takeUntil(this.#destroy),
    shareReplay(1)
  );

  readonly #stateFilterByKey = (
    key: keyof CityUI,
    overrider: BehaviorSubject<boolean>
  ) => {
    return this.cityinfo$.pipe(
      combineLatestWith(overrider),
      map(([cities, overrider]) => {
        if (overrider) {
          return [];
        }

        const items = cities.map(city => city[key]);
        const uniques = new Set(items);

        return Array.from(uniques);
      }),
      withLatestFrom(this.#store.state$, this.zipCode$),
      tap(([items, state, zipCode]) => {
        const hasPrefilledValue = Boolean(state[key]);
        const zipCodeHasChanged = zipCode !== state.postalCode;

        const control = this.addressForm.get(key);

        if (!zipCode || (hasPrefilledValue && !zipCodeHasChanged)) {
          control?.setValue(state[key] ?? '');

          return;
        }

        control?.setValue(items[0] ?? '');
      }),
      map(([items]) => items),
      takeUntil(this.#destroy)
    );
  };

  /** When there are multiple options,
   * we need to enable some way to the user
   * to exit of the select html element
   * and write the value manually
   */
  readonly #suburbOverrider$ = new BehaviorSubject<boolean>(false);
  readonly suburbs$ = this.#stateFilterByKey(
    'neighborhood',
    this.#suburbOverrider$
  );

  /** When there are multiple options,
   * we need to enable some way to the user
   * to exit of the select html element
   * and write the value manually
   */
  readonly #municipalityOverrider$ = new BehaviorSubject<boolean>(false);
  readonly municipalities$ = this.#stateFilterByKey(
    'municipality',
    this.#municipalityOverrider$
  );

  /** When there are multiple options,
   * we need to enable some way to the user
   * to exit of the select html element
   * and write the value manually
   */
  readonly #stateOverrider$ = new BehaviorSubject<boolean>(false);
  readonly states$ = this.#stateFilterByKey('state', this.#stateOverrider$);

  readonly address = toSignal(
    this.#store.state$.pipe(
      map(s => {
        return this.#getLastAddress(s);
      }),
      takeUntil(this.#destroy)
    )
  );

  readonly #isEditing = signal(false);
  readonly showAddressForm = computed(() => {
    const hasAddress = Boolean(this.address());
    const isEditing = this.#isEditing();

    return !hasAddress || (hasAddress && isEditing);
  });
  readonly showUpdateAddress = computed(() => {
    const hasAddress = Boolean(this.address());
    const isEditing = this.#isEditing();

    return hasAddress && !isEditing;
  });
  readonly showRollbackAddress = computed(() => {
    const hasAddress = Boolean(this.address());
    const isEditing = this.#isEditing();

    return hasAddress && isEditing;
  });

  constructor() {
    this.#iconRegister.registerIcons([iconSpinCircle]);
  }

  openForm(): void {
    this.#isEditing.set(true);
    this.enableModifiableControls();
  }

  hideAddressForm(): void {
    this.#isEditing.set(true);
  }

  async rollbackAddress(): Promise<void> {
    const store = this.#store.getAddressWithParticipant();

    if (store.participantId) {
      this.zipCodeControl.setValue(store.postalCode ?? '');

      await firstValueFrom(timer(this.DEBOUNCE_TIME + 1));

      this.exteriorNumberControl.setValue(store.exteriorNumber ?? '');
      this.interiorNumberControl.setValue(store.interiorNumber ?? '');
      this.streetControl.setValue(store.street ?? '');
      this.neighborhoodControl.setValue(store.neighborhood ?? '');
      this.municipalityControl.setValue(store.municipality ?? '');
      this.stateControl.setValue(store.state ?? '');
      this.referenceControl.setValue(store.reference ?? '');
      this.isMallControl.setValue(store.isMall ?? false);
    }
  }

  enableModifiableControls(): void {
    this.streetControl.enable();
    this.exteriorNumberControl.enable();
    this.interiorNumberControl.enable();
    this.zipCodeControl.enable();
    this.neighborhoodControl.enable();
    this.municipalityControl.enable();
    this.stateControl.enable();
    this.referenceControl.enable();
    this.isMallControl.enable();
  }

  disableModifiableControls(): void {
    this.streetControl.disable();
    this.exteriorNumberControl.disable();
    this.interiorNumberControl.disable();
    this.zipCodeControl.disable();
    this.neighborhoodControl.disable();
    this.municipalityControl.disable();
    this.stateControl.disable();
    this.referenceControl.disable();
    this.isMallControl.disable();
  }

  async confirmAddress(): Promise<void> {
    this.addressForm.markAllAsTouched();

    if (
      // normal case
      this.addressForm.valid ||
      // case when there is address prefilled
      // and no changes
      this.addressForm.disabled
    ) {
      const rawValue = this.addressForm.getRawValue();

      const result = await firstValueFrom(
        this.#dialog.open(AplazoConfirmDialogComponent, {
          data: {
            title:
              '¿Está seguro de confirmar la dirección? Una vez confirmada, no podrá modificarla',
            dynamicMessage: `Dirección a guardar: ${this.#getLastAddress(
              rawValue as any
            )}.`,
            cancelButton: 'Regresar a editar',
            acceptButton: 'Confirmar dirección',
          },
          enableClose: false,
          maxWidth: '320px',
        }).afterClosed$
      );

      if (result?.confirmation) {
        const token = this.#store.getToken();

        try {
          await firstValueFrom(
            this.#confirmAddress.execute({
              name: rawValue.name,
              street: rawValue.street,
              exteriorNumber: rawValue.exteriorNumber,
              interiorNumber: rawValue.interiorNumber ?? '',
              postalCode: rawValue.postalCode,
              neighborhood: rawValue.neighborhood,
              municipality: rawValue.municipality,
              state: rawValue.state,
              participantId: rawValue.participantId,
              token: token,
              reference: rawValue.reference,
              isMall: rawValue.isMall,
            })
          );

          this.#store.setEditionFinished(true);

          await firstValueFrom(
            defer(() =>
              this.#router.navigate([
                ROUTE_CONFIG.addressConfirmation,
                ROUTE_CONFIG.confirmedAddress,
              ])
            )
          );
        } catch (error) {
          console.warn('debug :: confirmAddress :: error :: ', error);
          this.#store.setIsAddressConfirmed(false);
        }
      }
    }
  }

  ngOnInit(): void {
    combineLatest({
      store: this.#store.state$,
      state: this.states$,
      municipality: this.municipalities$,
      suburb: this.suburbs$,
    })
      .pipe(
        takeUntil(this.#destroy),
        tap(({ store }) => {
          const hasPrefilledState = Boolean(store.state);
          const hasPrefilledMunicipality = Boolean(store.municipality);
          const hasPrefilledSuburb = Boolean(store.neighborhood);
          const hasPrefilledZipCode = Boolean(store.postalCode);

          if (
            !this.#isEditing() &&
            hasPrefilledState &&
            hasPrefilledMunicipality &&
            hasPrefilledSuburb &&
            hasPrefilledZipCode
          ) {
            this.disableModifiableControls();
          }
        })
      )
      .subscribe();

    combineLatest({
      state: this.stateControl.valueChanges.pipe(
        startWith(this.stateControl.value)
      ),
      municipality: this.municipalityControl.valueChanges.pipe(
        startWith(this.municipalityControl.value)
      ),
      neighborhood: this.neighborhoodControl.valueChanges.pipe(
        startWith(this.neighborhoodControl.value)
      ),
    })
      .pipe(
        takeUntil(this.#destroy),
        tap(({ neighborhood, municipality, state }) => {
          if (neighborhood === this.OVERRIDER_VALUE) {
            this.#suburbOverrider$.next(true);
          }
          if (municipality === this.OVERRIDER_VALUE) {
            this.#municipalityOverrider$.next(true);
          }
          if (state === this.OVERRIDER_VALUE) {
            this.#stateOverrider$.next(true);
          }
        })
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }

  #getLastAddress(address: WinnerProfileData): string | null {
    const street = address.street || '';
    const exteriorNumber = address.exteriorNumber || '';
    const interiorNumber = address.interiorNumber || '';
    const postalCode = address.postalCode || '';
    const neighborhood = address.neighborhood || '';
    const municipality = address.municipality || '';
    const state = address.state || '';

    if (
      street &&
      exteriorNumber &&
      postalCode &&
      neighborhood &&
      municipality &&
      state
    ) {
      return `${!street ? '' : street + ' '}${
        !exteriorNumber ? '' : exteriorNumber + ', '
      }${!interiorNumber ? '' : interiorNumber + ', '}${
        !neighborhood ? '' : neighborhood + ', '
      }${!municipality ? '' : municipality + ', '}${
        !state ? '' : state + ', '
      }C.P. ${postalCode}`;
    }

    return null;
  }
}

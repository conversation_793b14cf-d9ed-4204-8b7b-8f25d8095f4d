import { AsyncPipe } from '@angular/common';
import { Component, inject, OnDestroy } from '@angular/core';
import { RouterLink } from '@angular/router';
import { I18NService } from '@aplazo/i18n';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconChevronLeft } from '@aplazo/ui-icons';
import {
  combineLatest,
  map,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { ROUTE_CONFIG } from '../../../../../core/domain/config/app-routes.core';
import { WinnerProfileStore } from '../../services/winner-profile-store.service';

@Component({
  selector: 'app-confirmed-address',
  template: ` @if (vm$ | async; as context) {
    <main class="w-full max-w-2xl mx-auto mt-20">
      <header class="mb-12 flex justify-start">
        <a
          aplzButton
          aplzAppearance="basic"
          aplzColor="info"
          size="md"
          [fullWidth]="false"
          [routerLink]="routes.contestRegistration">
          <aplz-ui-icon name="chevron-left"></aplz-ui-icon>

          <span class="text-md ml-2">{{ context.textUI.backButton }}</span>
        </a>
      </header>

      <section class="grid grid-cols-1 gap-4">
        <figure class="mx-auto mb-12">
          <img
            class="w-40 aspect-auto"
            [src]="context.textUI.imageUrl"
            alt="confirmacion exitosa" />
        </figure>
        <h1 class="text-center text-2xl font-bold mb-8">
          {{ context.textUI.title }}
        </h1>

        <p class="text-center text-dark-secondary">
          {{ context.textUI.body }}
        </p>

        @if (context.textUI.supportLink) {
          <div class="col-span-full flex justify-center mt-10">
            <a
              aplzButton
              aplzAppearance="stroked"
              aplzColor="light"
              size="md"
              [href]="context.textUI.supportLink"
              target="_blank">
              Contactar a soporte
            </a>
          </div>
        }
      </section>
    </main>
  }`,
  imports: [RouterLink, AsyncPipe, AplazoButtonComponent, AplazoIconComponent],
})
export class ConfirmedAddressComponent implements OnDestroy {
  readonly #store = inject(WinnerProfileStore);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #i18n = inject(I18NService);

  readonly routes = ROUTE_CONFIG;
  readonly #destroy$ = new Subject<void>();
  readonly #state$ = this.#store.state$;

  readonly #textContentKey$: Observable<'success' | 'alreadyConfirmed'> =
    this.#state$.pipe(
      map(state => {
        if (!state.isConfirmed && state.finishEditing) {
          return 'success';
        }

        // TODO: handle edge cases to multiple possible states

        return 'alreadyConfirmed';
      })
    );

  readonly #textUI$ = this.#textContentKey$.pipe(
    switchMap(key =>
      this.#i18n.getTranslateObjectByKey<{
        title: string;
        body: string;
        backButton: string;
        imageUrl: string;
        supportLink?: string;
      }>({
        key,
        scope: 'address-confirmation',
      })
    )
  );

  readonly vm$ = combineLatest({
    state: this.#state$,
    textUI: this.#textUI$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#iconRegister.registerIcons([iconChevronLeft]);
  }

  ngOnDestroy(): void {
    this.#store.clearState();
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}

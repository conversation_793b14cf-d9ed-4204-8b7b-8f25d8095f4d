import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';

export const contestLabelRoutes = {
  Inscripción: ROUTE_CONFIG.contestRegistration,
  'Mis Registros': ROUTE_CONFIG.contestPosition,
} as const;

@Component({
  selector: 'app-layout-contest-entries',
  template: `
    <section class="min-h-[90vh] pt-2 md:pt-8 pb-[30vh]">
      <router-outlet></router-outlet>
    </section>
  `,
  imports: [RouterOutlet],
})
export class AplazoContestEntriesLayoutComponent {}

import { Injectable } from '@angular/core';
import { BehaviorSubject, distinctUntilChanged, map } from 'rxjs';
import { WinnerProfileData } from '../../domain/winner-profile';

@Injectable({
  providedIn: 'root',
})
export class WinnerProfileStore {
  readonly #default: WinnerProfileData = {
    token: null,
    participantId: null,
    finishEditing: null,
  };
  readonly #state = new BehaviorSubject<WinnerProfileData>(this.#default);

  readonly state$ = this.#state.asObservable();
  readonly token$ = this.state$.pipe(
    map(state => state.token ?? null),
    distinctUntilChanged()
  );
  readonly lastUpdate$ = this.state$.pipe(
    map(state => state.addressUpdatedAt ?? null),
    distinctUntilChanged()
  );
  readonly isAddressConfirmed$ = this.state$.pipe(
    map(state => state.isConfirmed ?? false),
    distinctUntilChanged()
  );

  getFinishEditing(): boolean {
    const state = this.#state.getValue();

    return state.finishEditing ?? false;
  }

  getToken(): string | null {
    const state = this.#state.getValue();

    return state.token ?? null;
  }

  getIsAddressConfirmed(): boolean {
    const state = this.#state.getValue();

    return state.isConfirmed ?? false;
  }

  getAddressWithParticipant(): WinnerProfileData {
    const currentState = this.#state.getValue();

    const participantId = currentState.participantId ?? '';
    const fullName = currentState.name ?? '';
    const street = currentState.street ?? '';
    const exteriorNumber = currentState.exteriorNumber ?? '';
    const interiorNumber = currentState.interiorNumber ?? '';
    const postalCode = currentState.postalCode ?? '';
    const neighborhood = currentState.neighborhood ?? '';
    const municipality = currentState.municipality ?? '';
    const state = currentState.state ?? '';
    const finishEditing = currentState.finishEditing ?? false;
    const reference = currentState.reference ?? '';
    const isMall = currentState.isMall ?? false;

    return {
      participantId,
      name: fullName,
      street,
      exteriorNumber,
      interiorNumber,
      postalCode,
      neighborhood,
      municipality,
      state,
      finishEditing,
      reference,
      isMall,
    };
  }

  setToken(token: string): void {
    const currentState = this.#state.getValue();

    if (currentState.token === token) {
      return;
    }

    this.#state.next({ ...currentState, token });
  }

  setIsAddressConfirmed(isConfirmed: boolean): void {
    const currentState = this.#state.getValue();

    if (currentState.isConfirmed === isConfirmed) {
      return;
    }

    this.#state.next({ ...currentState, isConfirmed });
  }

  setParticipantId(participantId: string): void {
    const currentState = this.#state.getValue();

    if (currentState.participantId === participantId) {
      return;
    }

    this.#state.next({ ...currentState, participantId });
  }

  setState(state: WinnerProfileData): void {
    const currentState = this.#state.getValue();

    if (
      currentState.name === state.name &&
      currentState.street === state.street &&
      currentState.exteriorNumber === state.exteriorNumber &&
      currentState.interiorNumber === state.interiorNumber &&
      currentState.postalCode === state.postalCode &&
      currentState.neighborhood === state.neighborhood &&
      currentState.addressUpdatedAt === state.addressUpdatedAt &&
      currentState.municipality === state.municipality &&
      currentState.state === state.state &&
      currentState.reference === state.reference &&
      currentState.isMall === state.isMall
    ) {
      return;
    }

    this.#state.next({ ...currentState, ...state });
  }

  setEditionFinished(finishEditing: boolean): void {
    const currentState = this.#state.getValue();

    if (currentState.finishEditing === finishEditing) {
      return;
    }

    this.#state.next({ ...currentState, finishEditing });
  }

  clearState(): void {
    this.#state.next(this.#default);
  }
}

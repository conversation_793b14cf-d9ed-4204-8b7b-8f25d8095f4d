import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  EMPTY,
  finalize,
  map,
  Observable,
  switchMap,
  take,
  timer,
} from 'rxjs';
import { LoginRequest } from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class DoWinnerLoginUseCase
  implements
    BaseUsecase<
      LoginRequest,
      Observable<{
        authToken: string;
        participantId: string;
      }>
    >
{
  readonly #repository = inject(ContestRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(
    credentials: LoginRequest
  ): Observable<{ authToken: string; participantId: string }> {
    const idLoader = this.#loader.show();

    try {
      if (
        !credentials.participantId ||
        credentials.participantId.trim() === '' ||
        !credentials.phoneNumber ||
        credentials.phoneNumber.trim() === ''
      ) {
        throw new RuntimeMerchantError(
          'El ID del participante y el número de teléfono son requeridos',
          'LoginUseCase::emptyCredentials'
        );
      }

      return timer(1000).pipe(
        take(1),
        switchMap(() =>
          this.#repository.login({
            participantId: credentials.participantId,
            phoneNumber: credentials.phoneNumber,
          })
        ),
        map(response => {
          return {
            authToken: response.authToken,
            participantId: credentials.participantId,
          };
        }),
        catchError(err => {
          return this.#errorHandler.handle<never>(err);
        }),
        finalize(() => {
          this.#loader.hide(idLoader);
        }),
        take(1)
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, EMPTY);
    }
  }
}

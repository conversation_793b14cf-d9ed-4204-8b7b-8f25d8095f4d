import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  EMPTY,
  finalize,
  Observable,
  take,
  tap,
  throwError,
} from 'rxjs';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class SendParticipantIdUsecase
  implements BaseUsecase<number, Observable<void>>
{
  readonly #repository = inject(ContestRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(phone: number): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!Guard.againstNullOrUndefined({ phone }, 'phone').succeeded) {
        throw new RuntimeMerchantError(
          'El número de teléfono es requerido',
          'SendParticipantIdUsecase::invalid::phone'
        );
      }

      Guard.againstInvalidNumbers(
        phone,
        undefined,
        'El número de teléfono debe ser un número válido'
      );

      if (phone.toString().length !== 10) {
        throw new RuntimeMerchantError(
          'El número de teléfono debe tener 10 dígitos',
          'SendParticipantIdUsecase::invalid::phone'
        );
      }

      return this.#repository.forgotParticipantCode(phone).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Enviado',
            message:
              'Se ha enviado un código de recuperación a tu número de teléfono',
          });
        }),

        catchError(err => {
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 404:
                this.#notifier.warning({
                  title:
                    'No encontramos un participante asociado a este número',
                });
                break;

              case 400:
                this.#notifier.warning({
                  title: 'Parece que la información no es correcta',
                  message:
                    'Por favor, verifica que el número de teléfono sea correcto',
                });
                break;

              default:
                this.#notifier.error({
                  title: 'Ocurrió un error al enviar el código de recuperación',
                  message: 'Estamos trabajando para resolver el problema',
                });
                break;
            }

            return EMPTY;
          }

          return this.#errorHandler.handle(err, EMPTY);
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}

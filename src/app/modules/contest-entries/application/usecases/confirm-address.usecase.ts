import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, tap, throwError } from 'rxjs';
import { ContestRepository } from '../../domain/repositories/contest.repository';
import {
  KeyOfWinnerProfileData,
  WinnerProfileData,
} from '../../domain/winner-profile';

@Injectable()
export class ConfirmAddressUsecase
  implements BaseUsecase<WinnerProfileData, Observable<void>>
{
  readonly #repository = inject(ContestRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(data: WinnerProfileData): Observable<void> {
    // Winner notifications disabled for MEXP-775
    // Return empty observable to prevent notifications
    return new Observable<void>(observer => {
      observer.complete();
    });
  }
}

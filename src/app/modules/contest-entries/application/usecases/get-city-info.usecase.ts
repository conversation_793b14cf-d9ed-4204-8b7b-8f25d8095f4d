import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, EMPTY, map, Observable, of, take, tap } from 'rxjs';
import { cityRepositoryToCityUI, CityUI } from '../../domain/city';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class GetCityInfoUsecase
  implements BaseUsecase<string, Observable<CityUI[]>>
{
  readonly #repository = inject(ContestRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(zipcode: string): Observable<CityUI[]> {
    try {
      Guard.againstInvalidNumbers(
        +zipcode,
        undefined,
        'El código postal debe ser un número válido'
      );

      if (zipcode.length !== 5) {
        throw new RuntimeMerchantError(
          'El código postal debe ser un número válido de 5 dígitos',
          'GetCityInfoUsecase::execute::invalidZipcode'
        );
      }

      return this.#repository.getCityInfoByZipCode(zipcode).pipe(
        take(1),
        map((cities) => cities.map(cityRepositoryToCityUI)),
        tap((cities) => {
          if (!Array.isArray(cities) || cities.length === 0) {
            throw new RuntimeMerchantError(
              'No se encontraron ciudades para el código postal. Por favor, verifique el código postal o ingrese la dirección manualmente.',
              'GetCityInfoUsecase::execute::noCitiesFound'
            );
          }
        }),
        catchError((e) => this.#errorHandler.handle(e, of([] as CityUI[])))
      );
    } catch (error) {
      return this.#errorHandler.handle(error, EMPTY);
    }
  }
}

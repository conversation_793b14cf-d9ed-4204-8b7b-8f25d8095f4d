import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, Observable, throwError } from 'rxjs';
import { ContestRepository } from '../../domain/repositories/contest.repository';
import { WinnerProfileResponse } from '../../domain/winner-profile';

@Injectable()
export class GetWinnerProfileUseCase
  implements BaseUsecase<string, Observable<WinnerProfileResponse>>
{
  readonly #repository = inject(ContestRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #notifier = inject(NotifierService);

  execute(token: string): Observable<WinnerProfileResponse> {
    // Winner notifications disabled for MEXP-775
    // Return empty observable to prevent winner profile retrieval
    return new Observable<WinnerProfileResponse>(observer => {
      observer.complete();
    });
  }
}

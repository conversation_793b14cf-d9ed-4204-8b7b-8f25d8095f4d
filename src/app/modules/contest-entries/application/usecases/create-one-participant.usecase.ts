import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatestWith,
  EMPTY,
  finalize,
  map,
  Observable,
  take,
  tap,
  timer,
} from 'rxjs';
import {
  ContestRegistrationUI,
  fromUIToContestRegistrationRepositoryRequest,
} from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class CreateOneParticipantUsecase
  implements
    BaseUsecase<
      ContestRegistrationUI,
      Observable<{ success: boolean; error?: string }>
    >
{
  readonly #repository = inject(ContestRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);

  execute(
    args: ContestRegistrationUI
  ): Observable<{ success: boolean; error?: string }> {
    const idLoader = this.#loader.show();

    try {
      const request = fromUIToContestRegistrationRepositoryRequest(args);

      return timer(1000).pipe(
        take(1),
        combineLatestWith([this.#repository.createOne(request)]),

        tap(() => {
          this.#notifier.success({
            title: 'Hemos recibido tu solicitud de inscripción',
            message:
              'Revisa tu celular, te enviamos un mensaje con información importante',
          });
        }),

        map(() => {
          return { success: true };
        }),

        catchError((e) => {
          if (
            e instanceof HttpErrorResponse &&
            e.status === 400 &&
            e.error?.code === 'APZRWS007'
          ) {
            throw new RuntimeMerchantError(
              'El participante ya se encuentra registrado',
              'CreateOneParticipantUsecase::execute::alreadyRegistered'
            );
          }

          return this.#errorHandler.handle<never>(e);
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(error, EMPTY);
    }
  }
}

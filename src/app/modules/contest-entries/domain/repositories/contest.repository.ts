import { Observable } from 'rxjs';
import { CityRepositoryResponse } from '../city';
import {
  ContestRegion,
  ContestRegistrationRequest,
  LoginRequest,
  LoginResponse,
  ParticipantDetailResponse,
  SendQRResponse,
} from '../contest';
import { WinnerProfileData, WinnerProfileResponse } from '../winner-profile';

export abstract class ContestRepository {
  abstract retriveLastTyCLink(): Observable<{ termsConditions: string }>;
  abstract retrieveRegions(): Observable<ContestRegion[]>;
  abstract createOne(request: ContestRegistrationRequest): Observable<void>;
  abstract getOneWithDetails(
    participantCode: string
  ): Observable<ParticipantDetailResponse>;
  abstract resendParticipantQR(
    participantId: string
  ): Observable<SendQRResponse>;
  abstract login(request: LoginRequest): Observable<LoginResponse>;
  abstract getCityInfoByZipCode(
    zipCode: string
  ): Observable<CityRepositoryResponse[]>;
  abstract getWinnerProfile(token: string): Observable<WinnerProfileResponse>;
  abstract confirmAddress(request: WinnerProfileData): Observable<void>;
  abstract forgotParticipantCode(phone: number): Observable<void>;
}

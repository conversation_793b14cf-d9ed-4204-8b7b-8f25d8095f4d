import {
  EMAIL_REGEXP,
  Guard,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { PREMIOS_APLAZO_ROLES, PremiosAplazoRole } from './roles';

export interface ContestRegion {
  id: number;
  state_name: string;
  state_cdc_code: string;
  state_c_code: string;
}

export interface ContestRegionUI {
  [key: string]: ContestRegion;
}

export interface ContestRegistrationRequest {
  fullName: string;
  phoneNumber: string;
  userMail?: string;
  region: string;
  participantRole?: string;
}

export interface ContestRegistrationUI {
  fullName: string;
  phone: string;
  region: string;
  role?: PremiosAplazoRole;
  email?: string;
}

export interface ContestRankingUI {
  data: ParticipantRankUI[];
  campaignFinishDate: string | null;
  isFutureDate: boolean;
  qrUrl: string | null;
  hasDetails: boolean;
  participantId?: string;
  tier?: string | number | null;
}

export interface ParticipantRank {
  rank: number;
  signup_completed_count: number | null;
}

export interface ParticipantDetailResponse {
  campaignParticipants: number;
  currentPosition: number;
  ranking: ParticipantRank[];
  endDateCampaign: string; // isodate
  qr: string;
  participantId: string;
  tier?: string | null;
}

export interface ParticipantRankUI {
  rank: number;
  totalRegistration: number;
  matched?: boolean;
}

export const fromParticipantDetailResponseToContestRankingUI = (
  resp: ParticipantDetailResponse
): Omit<ContestRankingUI, 'isFutureDate'> => {
  const data = resp.ranking.map(rank => ({
    rank: rank.rank,
    totalRegistration: rank.signup_completed_count || 0,
    matched: resp.currentPosition === rank.rank,
  }));

  return {
    data,
    campaignFinishDate: resp.endDateCampaign,
    qrUrl: resp.qr,
    hasDetails: resp.campaignParticipants > 0 && resp.currentPosition >= 0,
    participantId: resp.participantId,
    tier: resp.tier ?? null,
  };
};

export const fromUIToContestRegistrationRepositoryRequest = (
  args: ContestRegistrationUI
): ContestRegistrationRequest | never => {
  const errors = ['fullName', 'phone', 'region']
    .map(key => {
      const error = Guard.againstNullOrUndefined(args, key);

      return error.succeeded ? null : error.message;
    })
    .filter(Boolean);

  if (errors.length) {
    throw new RuntimeMerchantError(
      errors.join(', '),
      'ContestRegistrationRequest::fromUIToContestRegistrationRepositoryRequest::emptyArgument'
    );
  }

  if (!Guard.againstEmptyValue(args, 'fullName').succeeded) {
    throw new RuntimeMerchantError(
      'El nombre del participante es requerido',
      'UIToRepositoryRequest::emptyFullName'
    );
  }

  Guard.againstInvalidNumbers(
    +args.phone,
    '',
    'El teléfono del participante tiene que ser un número de 10 dígitos.'
  );

  const request: ContestRegistrationRequest = {
    fullName: args.fullName,
    phoneNumber: args.phone,
    region: args.region,
  };

  if (args.email && EMAIL_REGEXP.exec(args.email) != null) {
    request.userMail = args.email;
  }

  if (args.role && PREMIOS_APLAZO_ROLES.includes(args.role)) {
    request.participantRole = args.role;
  }

  return request;
};

export interface ContestParticipant {
  position: number;
  name: string;
  points: number;
}

export interface ContestRanking {
  data: ContestParticipant[];
  hasDetails: boolean;
  isFutureDate: boolean;
  campaignFinishDate: string;
  qrUrl: string;
  phoneNumber?: string;
  participantId: string;
}

export interface SendQRResponse {
  success: boolean;
  message: string;
}

export interface LoginRequest {
  participantId: string;
  phoneNumber: string;
}

export interface LoginResponse {
  authToken: string;
}

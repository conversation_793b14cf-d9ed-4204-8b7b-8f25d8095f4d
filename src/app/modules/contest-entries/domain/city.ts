export type CityRepositoryResponse = {
  id: number;
  postal_code: string;
  suburb: string;
  municipality: string;
  state: string;
  state_cdc_code: string;
  state_c_code: string;
};

export type CityUI = {
  postalCode: string;
  neighborhood: string;
  municipality: string;
  state: string;
};

export const cityRepositoryToCityUI = (
  city: CityRepositoryResponse
): CityUI => {
  return {
    state: city.state,
    postalCode: city.postal_code,
    neighborhood: city.suburb,
    municipality: city.municipality,
  };
};

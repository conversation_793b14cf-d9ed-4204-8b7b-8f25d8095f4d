export type WinnerProfileResponse = {
  name: string | null;
  street: string;
  exteriorNumber: string;
  interiorNumber: string;
  postalCode: string;
  neighborhood: string;
  addressUpdatedAt: string | null; // date in string format
  municipality: string;
  state: string;
  isConfirmed?: boolean | null;
  reference?: string | null;
  isMall?: boolean | null;
};

export type WinnerProfileData = Partial<WinnerProfileResponse> & {
  participantId: string | null;
  token?: string | null;
  finishEditing?: boolean | null;
};

export type KeyOfWinnerProfileData = keyof WinnerProfileData;

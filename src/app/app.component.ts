import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavbarComponent } from './modules/shared/navbar/navbar.component';
import { SwUpdate, VersionReadyEvent } from '@angular/service-worker';
import { filter } from 'rxjs';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavbarComponent],
  template: ` <app-navbar></app-navbar>
    <router-outlet></router-outlet>`,
})
export class AppComponent {
  private swUpdate = inject(SwUpdate);

  constructor() {
    this.checkForUpdates();
  }

  private checkForUpdates(): void {
    if (this.swUpdate.isEnabled) {
      this.swUpdate.versionUpdates
        .pipe(
          filter(
            (evt): evt is VersionReadyEvent => evt.type === 'VERSION_READY'
          )
        )
        .subscribe(() => {
          if (confirm('Hay una nueva versión disponible. ¿Desea cargarla?')) {
            window.location.reload();
          }
        });
    }
  }
}

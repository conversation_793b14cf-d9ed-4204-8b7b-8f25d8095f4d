import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import {
  Injectable,
  OnDestroy,
  PLATFORM_ID,
  signal,
  inject,
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { toObservable } from '@angular/core/rxjs-interop';
import {
  Observable,
  Subject,
  catchError,
  fromEvent,
  map,
  merge,
  of,
  switchMap,
  takeUntil,
} from 'rxjs';

interface ConnectionState {
  isOnline: boolean;
  lastChecked: Date;
}

@Injectable({
  providedIn: 'root',
})
export class OnlineStatusService implements OnDestroy {
  private readonly http = inject(HttpClient);
  private readonly toastr = inject(ToastrService);
  private readonly document = inject(DOCUMENT);
  private readonly platformId = inject(PLATFORM_ID);
  private readonly window = this.document.defaultView;

  private readonly destroy$ = new Subject<void>();
  private activeToastId?: number;

  private readonly connectionState = signal<ConnectionState>({
    isOnline: navigator.onLine,
    lastChecked: new Date(),
  });

  public readonly connectionStatus$ = toObservable(this.connectionState);

  constructor() {
    this.initializeConnectionMonitoring();
  }

  private initializeConnectionMonitoring(): void {
    if (!this.window || !isPlatformBrowser(this.platformId)) {
      return;
    }

    merge(
      of(this.window.navigator.onLine),
      fromEvent(this.window, 'online').pipe(map(() => true)),
      fromEvent(this.window, 'offline').pipe(map(() => false))
    )
      .pipe(
        switchMap(isOnline =>
          isOnline
            ? of(true)
            : this.verifyConnection().pipe(catchError(() => of(false)))
        ),
        takeUntil(this.destroy$)
      )
      .subscribe(isOnline => {
        this.updateConnectionState(isOnline);
        this.handleConnectionNotification(isOnline);
      });
  }

  private verifyConnection(): Observable<boolean> {
    return this.http.get('https://www.google.com').pipe(map(() => true));
  }

  private updateConnectionState(isOnline: boolean): void {
    this.connectionState.set({
      isOnline,
      lastChecked: new Date(),
    });
  }

  private handleConnectionNotification(isOnline: boolean): void {
    if (!isOnline) {
      this.activeToastId = this.toastr.warning(
        'Revise su conexión a internet',
        'Conexión no disponible',
        {
          disableTimeOut: true,
          closeButton: false,
          tapToDismiss: false,
          positionClass: 'toast-bottom-full-width',
        }
      ).toastId;
    } else if (this.activeToastId !== undefined) {
      this.toastr.remove(this.activeToastId);
      this.activeToastId = undefined;
    }
  }

  public isOnline(): boolean {
    return this.connectionState().isOnline;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

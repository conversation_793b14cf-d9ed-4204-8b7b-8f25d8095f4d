import { DOCUMENT } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { BrowserLocalStorageService } from '../application/services/browser-local-persistence.service';

@Injectable({ providedIn: 'root' })
export class LocalPersistenceWithNativeApiService
  implements BrowserLocalStorageService
{
  readonly #document = inject(DOCUMENT);
  readonly #localStorage: Storage | null =
    this.#document.defaultView?.localStorage ?? null;

  public getItem(key: string): string | null {
    return this.#localStorage?.getItem(key) ?? null;
  }

  public setItem(key: string, value: any): void {
    this.#localStorage?.setItem(key, value);
  }

  public removeItem(key: string): void {
    this.#localStorage?.removeItem(key);
  }

  public clearStorage(): void {
    this.#localStorage?.clear();
  }
}

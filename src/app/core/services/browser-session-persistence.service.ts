import { DOCUMENT } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { BrowserSessionStorageService } from '../application/services/browser-local-persistence.service';

@Injectable({ providedIn: 'root' })
export class SessionPersistenceWithNativeApiService
  implements BrowserSessionStorageService
{
  readonly #document = inject(DOCUMENT);
  readonly #sessionStorage: Storage | null =
    this.#document.defaultView?.sessionStorage ?? null;

  public getItem(key: string): string | null {
    return this.#sessionStorage?.getItem(key) ?? null;
  }

  public setItem(key: string, value: any): void {
    this.#sessionStorage?.setItem(key, value);
  }

  public removeItem(key: string): void {
    this.#sessionStorage?.removeItem(key);
  }

  public clearStorage(): void {
    this.#sessionStorage?.clear();
  }
}

import { Injectable, inject } from '@angular/core';
import {
  GtmMerchantEventDescription,
  TagManagerService,
} from '@aplazo/front-analytics/tag-manager';
import {
  AnalyticsService,
  GTMMerchantEventName,
} from '../application/services/analytics.service';

@Injectable({ providedIn: 'root' })
export class AnalyticsWithGtmService implements AnalyticsService {
  readonly #tagService: TagManagerService = inject(TagManagerService);

  track(
    eventName: GTMMerchantEventName,
    data?: Partial<GtmMerchantEventDescription>
  ) {
    const defaultDescription = {
      genericInfo: '',
      startDate: '',
      endDate: '',
      status: '',
      searchTerm: '',
      pageNum: 0,
      pageSize: 0,
      loanId: 0,
      graphType: '',
      buttonName: '',
    };

    this.#tagService.trackEvent({
      event: eventName,
      description: data ? { ...defaultDescription, ...data } : undefined,
    });
  }
}

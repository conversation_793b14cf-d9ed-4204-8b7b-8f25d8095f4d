import {
  EnvironmentProviders,
  InjectionToken,
  makeEnvironmentProviders,
} from '@angular/core';

export const ROUTE_CONFIG = {
  contestEntries: 'premios-aplazo',
  contestPosition: 'position',
  contestRegistration: 'registration',
  addressConfirmation: 'addressconfirmation',

  // for use in the winner address flow
  loginWinner: 'login-winner',
  addressForm: 'address-form',
  confirmedAddress: 'confirmed-address',
  forgotParticipantCode: 'forgot-participant-code',
} as const;

export type RouteConfig = typeof ROUTE_CONFIG;

export const APP_ROUTES = new InjectionToken<RouteConfig>('APP_ROUTES');

export function provideAppRoutes(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: APP_ROUTES,
      useValue: ROUTE_CONFIG,
    },
  ]);
}

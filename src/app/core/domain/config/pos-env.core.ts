import {
  EnvironmentProviders,
  InjectionToken,
  makeEnvironmentProviders,
} from '@angular/core';
import { environment } from '../../../../environments/environment';

export const POS_ENVIRONMENT_CORE = new InjectionToken<PosEnvironmentCoreType>(
  'POS_ENVIRONMENT_CORE'
);

export type PosEnvironmentCoreType = Readonly<{
  production: boolean;
  API_URL: string;
  APP_NAME: string;
  POS_API_URL: string;
  POS_WS_URL: string;
  HOSTNAME: string;
  ZENDESK: {
    SDK: string;
    ID: string;
  };
  LANDINGPAGE: string;
  REGISTER_MERCHANT_URL: string;
  CUSTOMER_LOGIN_URL: string;
  AUTH_API_URL: string;
  GTM_ID: string;
  PROMO_API_URL: string;
  FEATURE_FLAGS_API_KEY: string;
  FEATURE_FLAGS_ENV: string;
  DATADOG_APPLICATION_ID: string;
  DATADOG_CLIENT_TOKEN: string;
  DATADOG_ENV: string;
  DATADOG_SERVICE: string;
  MERCHANT_DASH_API_URL: string;
  WEBCHAT_CONFIG: {
    apiKey: string;
    brandId: string;
  };
  PAYMENTS_CORE_API_URL: string;
  mspaymentMicro: string;
  KOUNT: {
    clientID: string;
    hostname: string;
    isSinglePageApp: boolean;
  };
  msMerchantMicro: string;
  bifrostUrl: string;
  customerRegistrationMicro: string;
  exposedPublicApiUrl: string;
  i18nUrl: string;
}>;

const posEnv: PosEnvironmentCoreType = {
  production: environment.production,
  API_URL: environment.API_URL,
  APP_NAME: environment.APP_NAME,
  POS_API_URL: environment.POS_API_URL,
  POS_WS_URL: environment.POS_WS_URL,
  HOSTNAME: environment.HOSTNAME,
  ZENDESK: {
    SDK: environment.ZENDESK.SDK,
    ID: environment.ZENDESK.ID,
  },
  LANDINGPAGE: environment.LANDINGPAGE,
  REGISTER_MERCHANT_URL: environment.REGISTER_MERCHANT_URL,
  CUSTOMER_LOGIN_URL: environment.CUSTOMER_LOGIN_URL,
  GTM_ID: environment.GTM_ID,
  AUTH_API_URL: environment.AUTH_API_URL,
  PROMO_API_URL: environment.PROMO_API_URL,
  FEATURE_FLAGS_API_KEY: environment.FEATURE_FLAGS_API_KEY,
  FEATURE_FLAGS_ENV: environment.FEATURE_FLAGS_ENV,
  DATADOG_APPLICATION_ID: environment.DATADOG.applicationId,
  DATADOG_CLIENT_TOKEN: environment.DATADOG.clientToken,
  DATADOG_ENV: environment.DATADOG.env,
  DATADOG_SERVICE: environment.DATADOG.service,
  MERCHANT_DASH_API_URL: environment.MERCHANT_DASH_API_URL,
  WEBCHAT_CONFIG: {
    apiKey: environment.WEBCHAT_CONFIG.apiKey,
    brandId: environment.WEBCHAT_CONFIG.brandId,
  },
  PAYMENTS_CORE_API_URL: environment.PAYMENTS_CORE.apiURL,
  mspaymentMicro: environment.msPaymentMicro,
  KOUNT: {
    clientID: environment.KOUNT.clientID,
    hostname: environment.KOUNT.hostname,
    isSinglePageApp: environment.KOUNT.isSinglePageApp,
  },
  msMerchantMicro: environment.msMerchantMicro,
  bifrostUrl: environment.bifrostUrl,
  customerRegistrationMicro: environment.customerRegistrationMicro,
  exposedPublicApiUrl: environment.exposedPublicApiUrl,
  i18nUrl: environment.i18nUrl,
};

export const posEnvironmentCore: PosEnvironmentCoreType = Object.freeze(posEnv);

export function provideCoreEnvironments(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: POS_ENVIRONMENT_CORE,
      useValue: posEnvironmentCore,
    },
  ]);
}

import {
  EMPTY,
  Observable,
  UnaryFunction,
  catchError,
  pipe,
  retry,
  take,
} from 'rxjs';
import { environment } from '../../../../environments/environment';
import {
  AddProductEventDataFoundation,
  DataFoundationOrigin,
  LoginEventDataFoundation,
  NewOrderEventDataFoundation,
  ShareByQrEventDataFoundation,
  ShareOrderEventDataFoundation,
  StoreFrontEventDataFoundation,
} from '../events.data-foundation';

export abstract class EventService {
  protected readonly baseUrl = environment.POS_API_URL;
  protected readonly origin = DataFoundationOrigin.POSUI;

  abstract login(data: LoginEventDataFoundation): Observable<void>;

  abstract storeFront(data: StoreFrontEventDataFoundation): Observable<void>;

  abstract newOrder(data: NewOrderEventDataFoundation): Observable<void>;

  abstract addProduct(data: AddProductEventDataFoundation): Observable<void>;

  abstract shareByQrCode(data: ShareByQrEventDataFoundation): Observable<void>;

  abstract shareOrder(data: ShareOrderEventDataFoundation): Observable<void>;

  abstract cancelOrder(data: ShareOrderEventDataFoundation): Observable<void>;

  protected putOriginInData<T>(data: T): T & { origin: DataFoundationOrigin } {
    return {
      ...data,
      origin: this.origin,
    };
  }

  protected observableProcess(): UnaryFunction<
    Observable<void>,
    Observable<void>
  > {
    return pipe(
      take(1),
      retry(0),
      catchError(() => EMPTY)
    );
  }
}

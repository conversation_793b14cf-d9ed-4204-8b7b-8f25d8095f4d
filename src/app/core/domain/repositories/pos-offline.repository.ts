import { UIDateRange } from '@aplazo/shared-ui';
import { Observable } from 'rxjs';
import { LoanRequest } from '../cart';
import { Order } from '../order.interface';
import { Challenge } from '../challenge';

export interface SendLinkRequestDTO {
  loanId: number;
  phoneNumber: string;
}

export interface CommonPosOfflineResponse<T> {
  content: T;
  error: unknown;
  code: number;
}

export abstract class PosOfflineRepository {
  abstract getTodayOrders(): Observable<Order[]>;

  abstract getHistoricalWeekOrders(
    startAt?: string,
    endAt?: string
  ): Observable<Order[]>;

  abstract getHistoricalReportByDate(
    startAt: string,
    endAt: string
  ): Observable<any>;

  abstract getHistoricalSummaryByDate(dateRange: UIDateRange): Observable<{
    totalOrder: number;
    totalSale: number;
    avgPrice: number;
  } | null>;

  abstract deleteOrder(orderId: number): Observable<any>;

  abstract createLoan(order: LoanRequest): Observable<Order>;

  abstract sendPaymentLink(
    request: SendLinkRequestDTO
  ): Observable<CommonPosOfflineResponse<any>>;

  abstract getQrImage(loanId: string): Observable<string>;

  abstract refundOrder(loanId: number): Observable<any>;

  abstract sendWSPaymentLink(
    request: SendLinkRequestDTO
  ): Observable<CommonPosOfflineResponse<any>>;

  abstract checkOrderStatus(
    loanId: number
  ): Observable<CommonPosOfflineResponse<Order>>;

}

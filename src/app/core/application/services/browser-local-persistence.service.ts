export abstract class BrowserPersistenceService {
  public abstract getItem(key: string): string | null;
  public abstract setItem(key: string, value: any): void;
  public abstract removeItem(key: string): void;
  public abstract clearStorage(): void;
}

export abstract class BrowserLocalStorageService extends BrowserPersistenceService {}
export abstract class BrowserSessionStorageService extends BrowserPersistenceService {}

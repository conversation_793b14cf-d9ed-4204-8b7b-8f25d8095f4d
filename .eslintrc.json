{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": ["aplz", "app"], "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}, {"files": ["*.html"], "excludedFiles": ["*inline-template-*.component.html"], "extends": ["plugin:prettier/recommended"], "rules": {"prettier/prettier": ["error", {"parser": "angular"}]}}]}
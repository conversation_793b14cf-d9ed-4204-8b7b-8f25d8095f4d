### STAGE 1: Build ###
FROM node:22.13.1-slim AS build

ARG ENV="dev"
ARG AUTH_TOKEN

ENV AUTH_TOKEN=$AUTH_TOKEN
ENV ENV=$ENV

WORKDIR /usr/src/app

RUN npm cache clean --force

COPY package*.json ./
COPY .npmrc ./

RUN npm ci --no-progress --loglevel=error --ignore-scripts

COPY . .

RUN node --run $ENV

### STAGE 2: Run ###
FROM nginx:1.26.2-alpine3.20-slim
COPY ./nginx-custom.conf /etc/nginx/conf.d/default.conf
COPY ./nginx.conf /etc/nginx
COPY --from=build /usr/src/app/dist/premios-aplazo/browser/ /usr/share/nginx/html
EXPOSE 80

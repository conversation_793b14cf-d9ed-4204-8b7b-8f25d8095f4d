{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"premios-aplazo": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/premios-aplazo", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.css", "node_modules/ngx-toastr/toastr.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "4kb"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "preserveSymlinks": true, "optimization": false, "extractLicenses": false, "sourceMap": true}, "stg": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "premios-aplazo:build:production"}, "development": {"buildTarget": "premios-aplazo:build:development"}, "stg": {"buildTarget": "premios-aplazo:build:stg"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "premios-aplazo:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.css", "node_modules/ngx-toastr/toastr.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css"], "scripts": [], "karmaConfig": "karma.conf.js", "browsers": "Chrome"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}, "analytics": false}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}
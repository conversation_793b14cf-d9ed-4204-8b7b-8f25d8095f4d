version: '3'
services:
  lpa_local:
    container_name: lpa_local
    image: lpa_local
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${ENV}
    env_file:
      - .env
    ports:
      - 4200:80

  lpa_local_dev:
    container_name: lpa_local_dev
    image: lpa_local_dev
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${ENV}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - '/usr/src/app/node_modules'
      - '.:/usr/src/app'
    read_only: true

  lpa_local_test:
    container_name: lpaLocalTest
    image: lpa_local_test
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${ENV}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - lpa_local_test_coverage_vol:/usr/src/app/coverage

volumes:
  lpa_local_test_coverage_vol:

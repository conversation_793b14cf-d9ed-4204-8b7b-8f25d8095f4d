{"name": "angular.merchant-premios-aplazo.landing-page", "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://aplazo.mx", "email": "<EMAIL>"}, "scripts": {"ng": "ng", "dev:dev": "ng serve --configuration development", "dev:stg": "ng serve --configuration stg", "dev:prod": "ng serve --configuration production", "build": "ng build", "build-stg": "ng build --configuration stg", "build-prod": "ng build --configuration production", "test": "ng test --no-watch --no-progress --browsers=ChromeHeadlessNoSandbox --code-coverage", "lint": "ng lint", "prepare": "husky"}, "private": true, "dependencies": {"@angular/animations": "^20.1.0", "@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/platform-browser-dynamic": "^20.1.0", "@angular/router": "^20.1.0", "@angular/service-worker": "^20.1.0", "@aplazo/front-analytics": "~3.3.0", "@aplazo/front-observability": "~3.3.0", "@aplazo/i18n": "~3.3.0", "@aplazo/merchant": "~3.3.0", "@aplazo/shared-ui": "~3.3.0", "@aplazo/ui-icons": "~3.3.0", "@aplazo/workers": "~3.3.0", "@auth0/angular-jwt": "~5.2.0", "@datadog/browser-logs": "~6.6.0", "@datadog/browser-rum": "~6.6.0", "@jsverse/transloco": "~7.4.0", "@ngneat/dialog": "~5.1.0", "@statsig/angular-bindings": "~3.16.2", "@statsig/session-replay": "~3.16.2", "@statsig/web-analytics": "~3.16.2", "date-fns": "~4.1.0", "date-fns-tz": "^3.1.3", "nanoid": "5.0.8", "ngx-toastr": "~19.0.0", "rxjs": "~7.8.0", "tslib": "^2.6.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@angular/language-service": "^20.1.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@playwright/test": "1.53.2", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^9.1.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "prettier": "~3.1.1", "prettier-eslint": "^16.1.2", "tailwindcss": "3.4.15", "typescript": "5.8.3"}}
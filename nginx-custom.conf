# Expires map
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   1y;
    application/javascript     1y;
    ~image/                    max;
    ~font/                     max;
}

server {
  listen 80;
  root   /usr/share/nginx/html;
  ignore_invalid_headers off;
  large_client_header_buffers 8 32k;
  server_name  localhost;
  server_tokens off;

  if ($http_x_forwarded_proto = 'http') {
	  return 301 https://$host$request_uri;
  }

  location / {
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;

    add_header Cache-Control "no-store";
    add_header Pragma "no-cache";
    add_header Expires 0;
  }

  # Cache static files
  location ~* \.(jpg|jpeg|gif|png|webp|json|ico|svg|woff|woff2|ttf|eot)$ {
      expires 1y;
      add_header Cache-Control "public";
  }

  location ~* \.(js|css) {
    add_header Cache-Control "max-age=604800, must-revalidate";
  }

  location ~* \.(html|htm) {
    add_header Cache-Control "no-store";
  }
}
